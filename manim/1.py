from manim import *
import os


# It is expected that a file named 'layout_manager.py' exists in the same directory.
# This file should contain the LayoutManager class with the specified static methods.
# For example:
# class LayoutManager:
#     @staticmethod
#     def get_safe_position_right_of(left_object, right_object_to_position, margin=0.5): ...
#     @staticmethod
#     def ensure_screen_bounds(obj, screen_width=config.frame_width, screen_height=config.frame_height, margin=0.2): ...
#     @staticmethod
#     def print_layout_debug(obj, name): ...
# etc.

class HuangYangTianTianEvent(Scene):
    def _create_image_mobject(self, asset_path: str, target_height: float, asset_aspect_ratio_wh: float = 1.0):
        """
        Helper function to create ImageMobject or a placeholder if the image is not found.
        asset_aspect_ratio_wh: width/height ratio, used for placeholder sizing.
        """
        if not os.path.exists(asset_path):
            print(f"WARNING: Image asset '{asset_path}' not found. Using a red placeholder rectangle.")
            placeholder_width = target_height * asset_aspect_ratio_wh
            placeholder_rect = Rectangle(width=placeholder_width, height=target_height, color=RED, fill_opacity=0.5)
            placeholder_text = Text("Not Found", font_size=16, color=WHITE).move_to(placeholder_rect.get_center())
            # Create a VGroup and manually set width/height for LayoutManager compatibility
            obj = VGroup(placeholder_rect, placeholder_text)
            obj.width = placeholder_width
            obj.height = target_height
            obj.set_opacity(0)  # Initial state for animation
            return obj

        image = ImageMobject(asset_path)
        image.set_height(target_height)  # Width will be scaled automatically maintaining aspect ratio
        image.set_opacity(0)  # Initial state: transparent
        return image

    def construct(self):
        """
        展示黄杨钿甜在学校成人礼佩戴高价耳环事件，以及从炫富质疑到家庭财富来源的争议焦点转移过程。
        总时长：19秒
        画布尺寸：1920×1080像素 (16:9)
        Manim坐标系：Width: approx 14.22 units, Height: 8.0 units. Origin at center.
        """
        self.camera.background_color = WHITE

        try:
            from layout_manager import LayoutManager
        except ImportError:
            print("CRITICAL ERROR: layout_manager.py not found. This script requires LayoutManager.")
            print("Please create layout_manager.py with the necessary static methods.")

            # Create a dummy LayoutManager to prevent immediate crash, but issue strong warning.
            class DummyLayoutManager:
                @staticmethod
                def get_safe_position_right_of(left_object, right_object_to_position, margin=0.5):
                    print(f"DUMMY_LAYOUT: get_safe_position_right_of for {right_object_to_position}")
                    return right_object_to_position.get_center() + RIGHT * (
                                left_object.width / 2 + right_object_to_position.width / 2 + margin)

                @staticmethod
                def get_safe_position_left_of(right_object, left_object_to_position, margin=0.5):
                    print(f"DUMMY_LAYOUT: get_safe_position_left_of for {left_object_to_position}")
                    return left_object_to_position.get_center() + LEFT * (
                                right_object.width / 2 + left_object_to_position.width / 2 + margin)

                @staticmethod
                def get_safe_position_above(bottom_object, top_object_to_position, margin=0.3):
                    print(f"DUMMY_LAYOUT: get_safe_position_above for {top_object_to_position}")
                    return top_object_to_position.get_center() + UP * (
                                bottom_object.height / 2 + top_object_to_position.height / 2 + margin)

                @staticmethod
                def get_safe_position_below(top_object, bottom_object_to_position, margin=0.3):
                    print(f"DUMMY_LAYOUT: get_safe_position_below for {bottom_object_to_position}")
                    return bottom_object_to_position.get_center() + DOWN * (
                                top_object.height / 2 + bottom_object_to_position.height / 2 + margin)

                @staticmethod
                def check_overlap(obj1, obj2, margin=0.1): return False

                @staticmethod
                def print_layout_debug(obj, name):
                    obj_center = obj.get_center() if hasattr(obj, 'get_center') else [0, 0, 0]
                    obj_width = obj.width if hasattr(obj, 'width') else 0
                    obj_height = obj.height if hasattr(obj, 'height') else 0
                    print(
                        f"DUMMY_LAYOUT DEBUG - {name}: Pos: {obj_center}, Width: {obj_width:.2f}, Height: {obj_height:.2f}")

                @staticmethod
                def ensure_screen_bounds(obj, screen_width=config.frame_width, screen_height=config.frame_height,
                                         margin=0.2):
                    print(f"DUMMY_LAYOUT: ensure_screen_bounds for {obj}")

            LayoutManager = DummyLayoutManager
            # This part is crucial for making the script runnable even if layout_manager.py is missing,
            # while still adhering to the "must use LayoutManager" by calling its (dummy) methods.

        # Element Creation
        # Scene 1 Elements (0.0s - 5.0s)
        adult_ceremony_photo = self._create_image_mobject("黄杨钿甜成人礼现场照.jpg", target_height=4,
                                                          asset_aspect_ratio_wh=16 / 9)
        huang_yang_tian_tian_char_s1 = self._create_image_mobject("image.character", target_height=4,
                                                                  asset_aspect_ratio_wh=3 / 4)

        date_text_s1 = Text("2025年5月", font_size=24, color=BLACK, font="SimHei").set_opacity(0)
        title_text_s1 = Text("黄杨钿甜成人礼佩戴名贵耳环引争议", font_size=32, color=BLACK, font="SimHei",
                             weight=BOLD).set_opacity(0)

        # Scene 2 Elements (5.0s - 9.0s)
        earring_prop_s2 = self._create_image_mobject("image.prop", target_height=4.2, asset_aspect_ratio_wh=1.0)
        price_text_s2 = Text("疑似价值230万元格拉夫耳环", font_size=28, color=YELLOW, font="SimHei",
                             weight=BOLD).set_opacity(0)
        comparison_text_s2 = Text("(≈普通家庭30年收入)", font_size=20, color=LIGHT_GRAY, font="SimHei").set_opacity(0)

        # Scene 3 Elements (9.0s - 13.0s)
        hot_topic_screenshot_s3 = self._create_image_mobject("网络热议截图.png", target_height=4.5,
                                                             asset_aspect_ratio_wh=16 / 9)
        hot_topic_label_s3 = Text("网络热议", font_size=26, color=BLACK, font="SimHei", weight=BOLD).set_opacity(0)
        keyword_xuanfu_s3 = Text("炫富", font_size=24, color=RED, font="SimHei").set_opacity(0)
        keyword_shechi_s3 = Text("奢侈", font_size=24, color=BLUE, font="SimHei").set_opacity(0)
        keyword_jiajing_s3 = Text("家境", font_size=24, color=GREEN, font="SimHei").set_opacity(0)
        keyword_zhiyi_s3 = Text("质疑", font_size=24, color=PURPLE, font="SimHei").set_opacity(0)

        # Scene 4 Elements (13.0s - 19.0s)
        huang_yang_tian_tian_char_s4 = self._create_image_mobject("image.character", target_height=3.3,
                                                                  asset_aspect_ratio_wh=3 / 4)
        quote_text_s4 = Text("'向妈妈借的'", font_size=30, color=BLACK, font="SimHei", slant=ITALIC).set_opacity(0)
        controversy_shift_text_s4 = Text("从炫富质疑到财富来源争议", font_size=32, color=GRAY, font="SimHei",
                                         weight=BOLD).set_opacity(0)
        controversy_shift_text_s4.scale(0.1)  # Initial state: small for "从小到大出现" effect

        # Timings (absolute start times for scenes/segments)
        time_s1_start = 0.0
        time_s1_anim_dur = 2.0  # Duration of intro animations for Scene 1
        time_s1_end = 5.0

        time_s2_start = 5.0
        time_s2_transition_dur = 1.0  # Duration of transition from S1 to S2
        time_s2_flash_dur = 0.5
        time_s2_price_text_dur = 0.75
        time_s2_comp_text_dur = 0.75
        time_s2_end = 9.0

        time_s3_start = 9.0
        time_s3_main_anim_block_dur = 1.8  # Max duration of complex animation block
        time_s3_end = 13.0

        time_s4_start = 13.0
        time_s4_transition_dur = 1.0
        time_s4_quote_write_dur = 0.7
        time_s4_quote_emphasis_dur = 0.8
        time_s4_controversy_text_appear_at = 15.0  # Absolute time
        time_s4_controversy_text_appear_dur = 0.5
        time_s4_controversy_text_color_dur = 1.0
        time_s4_end = 19.0

        current_time = time_s1_start

        # --- Scene 1 (0.0s - 5.0s) ---
        adult_ceremony_photo.move_to([-3.5, 0, 0])
        huang_yang_tian_tian_char_s1.move_to([4.5, 0, 0])
        date_text_s1.move_to([-6, 3.5, 0])
        title_text_s1.move_to([0, 3, 0])

        self.play(
            adult_ceremony_photo.animate(run_time=time_s1_anim_dur, rate_func=smooth).set_opacity(1).scale(1.05),
            huang_yang_tian_tian_char_s1.animate(run_time=time_s1_anim_dur, rate_func=smooth).set_opacity(1).scale(1.1),
            FadeIn(date_text_s1, shift=RIGHT, run_time=1.0),
            title_text_s1.animate(run_time=1.0).set_opacity(1).shift(DOWN * 0.5),  # From above
            run_time=time_s1_anim_dur
        )
        for obj, name in [(adult_ceremony_photo, "成人礼现场照_S1"), (huang_yang_tian_tian_char_s1, "黄杨钿甜_S1"),
                          (date_text_s1, "日期_S1"), (title_text_s1, "标题_S1")]:
            LayoutManager.ensure_screen_bounds(obj);
            LayoutManager.print_layout_debug(obj, name)

        self.wait(time_s1_end - (current_time + time_s1_anim_dur))
        current_time = time_s1_end

        # --- Transition to Scene 2 (at 5.0s, duration 1.0s) ---
        earring_prop_s2_target_state = earring_prop_s2.copy().move_to(ORIGIN).set_height(4.2).set_opacity(1)
        self.play(
            Transform(huang_yang_tian_tian_char_s1, earring_prop_s2_target_state),  # HYTT char becomes earring
            FadeOut(adult_ceremony_photo),
            FadeOut(date_text_s1),
            FadeOut(title_text_s1),
            run_time=time_s2_transition_dur
        )
        current_time += time_s2_transition_dur  # current_time = 6.0s
        # huang_yang_tian_tian_char_s1 is now transformed; earring_prop_s2 is the active earring Mobject
        earring_prop_s2 = huang_yang_tian_tian_char_s1  # The transformed object is now our earring
        LayoutManager.ensure_screen_bounds(earring_prop_s2);
        LayoutManager.print_layout_debug(earring_prop_s2, "耳环_S2")

        # --- Scene 2 (elements appear from 6.0s, scene ends at 9.0s) ---
        # Flash at 6.0s
        self.play(Flash(earring_prop_s2, color=WHITE, flash_radius=earring_prop_s2.height * 1.2, num_lines=16,
                        run_time=time_s2_flash_dur))
        current_time_for_s2_elements = current_time + time_s2_flash_dur  # 6.5s

        price_text_s2.next_to(earring_prop_s2, DOWN, buff=0.3)
        LayoutManager.ensure_screen_bounds(price_text_s2);
        LayoutManager.print_layout_debug(price_text_s2, "价格标注_S2")

        # comparison_text_s2 placed below price_text_s2 using LayoutManager
        comparison_text_s2.move_to(LayoutManager.get_safe_position_below(price_text_s2, comparison_text_s2, margin=0.1))
        LayoutManager.ensure_screen_bounds(comparison_text_s2);
        LayoutManager.print_layout_debug(comparison_text_s2, "收入对比_S2")

        self.play(FadeIn(price_text_s2, shift=UP), run_time=time_s2_price_text_dur)
        current_time_for_s2_elements += time_s2_price_text_dur  # 6.5 + 0.75 = 7.25s

        self.play(FadeIn(comparison_text_s2), run_time=time_s2_comp_text_dur)
        current_time_for_s2_elements += time_s2_comp_text_dur  # 7.25 + 0.75 = 8.0s

        self.wait(time_s2_end - current_time_for_s2_elements)  # Wait 9.0 - 8.0 = 1.0s
        current_time = time_s2_end  # current_time = 9.0s

        # --- Scene 3 (9.0s - 13.0s) ---
        # earring_prop_s2 (large center) transforms to earring_prop_s3_small (small top-left)
        earring_prop_s3_small_target_state = earring_prop_s2.copy().set_height(1.5).move_to([-5, 3, 0]).set_opacity(1)
        # hot_topic_screenshot_s3 starts off-screen right
        hot_topic_screenshot_s3.set_opacity(0).move_to(RIGHT * (
                    config.frame_width / 2 + hot_topic_screenshot_s3.width / 2 + 0.2) + UP * hot_topic_screenshot_s3.get_y())  # Initial offscreen pos

        # Position S3 texts
        keyword_xuanfu_s3.move_to([1, -2, 0]);
        keyword_shechi_s3.move_to([2.2, -2, 0]);  # Adjusted for spacing
        keyword_jiajing_s3.move_to([3.4, -2, 0]);
        keyword_zhiyi_s3.move_to([4.6, -2, 0]);  # Adjusted for spacing
        hot_topic_label_s3.next_to(hot_topic_screenshot_s3.copy().move_to([2, 0, 0]), UP,
                                   buff=0.2)  # Position relative to target screenshot loc

        self.play(
            AnimationGroup(
                FadeOut(price_text_s2, run_time=0.5),
                FadeOut(comparison_text_s2, run_time=0.5),
                Transform(earring_prop_s2, earring_prop_s3_small_target_state, run_time=1.0),
                hot_topic_screenshot_s3.animate(run_time=1.0).move_to([2, 0, 0]).set_opacity(1),
                Succession(Wait(0.0), GrowFromCenter(keyword_xuanfu_s3, run_time=0.3)),  # 9.0-9.3s
                Succession(Wait(0.5), GrowFromCenter(keyword_shechi_s3, run_time=0.3)),  # 9.5-9.8s
                Succession(Wait(1.0), GrowFromCenter(keyword_jiajing_s3, run_time=0.3)),  # 10.0-10.3s
                Succession(Wait(1.0), Write(hot_topic_label_s3, run_time=0.5)),  # Label at 10.0-10.5s
                Succession(Wait(1.5), GrowFromCenter(keyword_zhiyi_s3, run_time=0.3)),  # 10.5-10.8s
                lag_ratio=0  # All Successions start their Wait timers at t=0 of this AnimationGroup
            ),
            run_time=time_s3_main_anim_block_dur  # Duration of this entire animation sequence
        )
        current_time += time_s3_main_anim_block_dur  # 9.0 + 1.8 = 10.8s
        earring_prop_s3_small = earring_prop_s2  # Transformed object

        for obj, name in [(earring_prop_s3_small, "耳环_S3"), (hot_topic_screenshot_s3, "网络热议截图_S3"),
                          (hot_topic_label_s3, "网络热议标签_S3"), (keyword_xuanfu_s3, "炫富_S3"),
                          (keyword_shechi_s3, "奢侈_S3"), (keyword_jiajing_s3, "家境_S3"),
                          (keyword_zhiyi_s3, "质疑_S3")]:
            LayoutManager.ensure_screen_bounds(obj);
            LayoutManager.print_layout_debug(obj, name)

        self.wait(time_s3_end - current_time)  # 13.0 - 10.8 = 2.2s
        current_time = time_s3_end  # current_time = 13.0s

        # --- Transition to Scene 4 (at 13.0s, duration 1.0s) ---
        s3_elements_to_fade = VGroup(hot_topic_screenshot_s3, hot_topic_label_s3, keyword_xuanfu_s3,
                                     keyword_shechi_s3, keyword_jiajing_s3, keyword_zhiyi_s3, earring_prop_s3_small)

        huang_yang_tian_tian_char_s4.move_to([-4.5, 0, 0]).set_opacity(0)  # Prepare for FadeIn
        LayoutManager.ensure_screen_bounds(huang_yang_tian_tian_char_s4);
        LayoutManager.print_layout_debug(huang_yang_tian_tian_char_s4, "黄杨钿甜_S4")

        self.play(
            FadeOut(s3_elements_to_fade, shift=DOWN * 0.5),
            FadeIn(huang_yang_tian_tian_char_s4, scale=0.8),  # Appears smaller
            run_time=time_s4_transition_dur
        )
        current_time += time_s4_transition_dur  # current_time = 14.0s

        # --- Scene 4 (elements appear from 13.0s, scene ends at 19.0s) ---
        quote_text_s4.move_to([0, 2, 0])
        LayoutManager.ensure_screen_bounds(quote_text_s4);
        LayoutManager.print_layout_debug(quote_text_s4, "引言_S4")
        self.play(Write(quote_text_s4), run_time=time_s4_quote_write_dur)  # 14.0s -> 14.7s
        current_time += time_s4_quote_write_dur

        self.play(quote_text_s4.animate.scale(1.15).set_color(RED),
                  rate_func=lambda t: there_and_back_with_pause(t, pause_ratio=0.3),
                  run_time=time_s4_quote_emphasis_dur)
        # Color change is permanent. Scale effect is temporary.
        quote_text_s4.scale(
            1 / 1.15)  # Reset scale if there_and_back doesn't reset it fully for the object's properties
        current_time += time_s4_quote_emphasis_dur  # 14.7s + 0.8s = 15.5s

        # Wait until 15.0s absolute time if needed
        wait_for_controversy_text = time_s4_controversy_text_appear_at - current_time
        if wait_for_controversy_text > 0:
            self.wait(wait_for_controversy_text)
            current_time += wait_for_controversy_text

        controversy_shift_text_s4.move_to([0, -3, 0])  # Already scaled small
        LayoutManager.ensure_screen_bounds(controversy_shift_text_s4);
        LayoutManager.print_layout_debug(controversy_shift_text_s4, "争议转变_S4")

        self.play(controversy_shift_text_s4.animate.set_opacity(1).scale(10.0),
                  run_time=time_s4_controversy_text_appear_dur)  # Scale factor 10 because initial was 0.1
        current_time += time_s4_controversy_text_appear_dur  # e.g. 15.0 + 0.5 = 15.5s

        self.play(controversy_shift_text_s4.animate.set_color(RED), run_time=time_s4_controversy_text_color_dur)
        current_time += time_s4_controversy_text_color_dur  # e.g. 15.5 + 1.0 = 16.5s

        self.wait(time_s4_end - current_time)  # Wait for 19.0 - 16.5 = 2.5s
        current_time = time_s4_end  # current_time = 19.0s

        self.wait(1)  # Final pause beyond documented time