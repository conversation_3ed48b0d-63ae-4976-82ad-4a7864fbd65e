from manim import *
import os
from layout_manager import LayoutManager  # 确保 layout_manager.py 在同一目录或 PYTHONPATH 中

# --- Configuration ---
ASSET_PATH_PREFIX = "assets"  # 存放所有图片资源的文件夹
DEFAULT_CHINESE_FONT = "SimHei"  # 用于中文的默认字体，确保系统已安装


# --- Helper function for asset paths ---
def get_image_path(filename):
    # For assets like "image.character" which might not have an extension in the doc
    base, ext = os.path.splitext(filename)
    if not ext:  # If no extension provided in the doc (e.g. "image.character")
        # Try common image extensions
        for potential_ext in [".png", ".jpg", ".jpeg"]:
            path_with_ext = os.path.join(ASSET_PATH_PREFIX, base + potential_ext)
            if os.path.exists(path_with_ext):
                return path_with_ext
        # If still not found, return the original name with assets prefix for os.path.exists check
        original_path = os.path.join(ASSET_PATH_PREFIX, filename)
        if not os.path.exists(original_path):
            print(f"警告：找不到图片文件 {original_path} 或其常见扩展名版本。")
        return original_path  # Let ImageMobject handle it, or fail
    else:  # Extension provided
        path = os.path.join(ASSET_PATH_PREFIX, filename)
        if not os.path.exists(path):
            print(f"警告：找不到图片文件 {path}")
        return path


class HYTControversyAnimation(Scene):
    def construct(self):
        # 设置背景色和相机
        self.camera.background_color = WHITE
        self.camera.frame_width = 14
        self.camera.frame_height = 8  # 16:9 aspect ratio

        # LayoutManager.setup(self.camera.frame_width, self.camera.frame_height)

        # 定义常用元素名称，方便管理
        element_names = {
            "adult_ceremony_photo": "成人礼现场照",
            "hyt_portrait": "黄杨钿甜肖像",
            "date_text": "日期文字",
            "title1_text": "标题1",
            "earring_prop": "耳环道具",
            "price_text": "价格文字",
            "income_comparison_text": "收入对比文字",
            "hot_topic_screenshot": "热议截图",
            "hot_topic_text": "网络热议文字",
            "keyword_xuanfu": "关键词炫富",
            "keyword_shechi": "关键词奢侈",
            "keyword_jiajing": "关键词家境",
            "keyword_zhiyi": "关键词质疑",
            "quote_text": "引言文字",
            "title2_text": "标题2"
        }

        # 时间线段 0.0s - 5.0s
        self.play_scene_1(element_names)

        # 时间线段 5.0s - 9.0s
        self.play_scene_2(element_names)

        # 时间线段 9.0s - 13.0s
        self.play_scene_3(element_names)

        # 时间线段 13.0s - 19.0s
        self.play_scene_4(element_names)

        self.wait(1)  # 额外等待1秒结束

    def _create_image(self, asset_name, design_doc_height, aspect_ratio, position_xy, name_key, element_names):
        img_path = get_image_path(asset_name)
        if not os.path.exists(img_path):
            print(f"严重警告: 图像文件 {img_path} 未找到。将使用占位符。")
            img = Rectangle(width=design_doc_height * aspect_ratio, height=design_doc_height, color=RED,
                            fill_opacity=0.5)
            Text("Image\nNot Found", font_size=20, color=BLACK).move_to(img.get_center())
        else:
            img = ImageMobject(img_path)
            img.height = design_doc_height
            img.width = design_doc_height * aspect_ratio  # 保持宽高比

        img.move_to(np.array([position_xy[0], position_xy[1], 0]))

        LayoutManager.add_object(img, element_names[name_key])
        LayoutManager.ensure_screen_bounds(img, LayoutManager._screen_width, LayoutManager._screen_height)
        # LayoutManager.print_layout_debug(img, element_names[name_key])
        return img

    def _create_text(self, text_content, font_size_px, color, position_xy, name_key, element_names, weight=NORMAL):
        # Manim font_size is not pixels, this is an approximate mapping
        manim_font_size = font_size_px * 1.5  # Adjust multiplier as needed

        txt = Text(text_content, font_size=manim_font_size, color=color, font=DEFAULT_CHINESE_FONT, weight=weight)
        txt.move_to(np.array([position_xy[0], position_xy[1], 0]))

        LayoutManager.add_object(txt, element_names[name_key])
        LayoutManager.ensure_screen_bounds(txt, LayoutManager._screen_width, LayoutManager._screen_height)
        # LayoutManager.print_layout_debug(txt, element_names[name_key])
        return txt

    def play_scene_1(self, el_names):
        # --- 元素创建 (0.0s - 5.0s) ---
        # 黄杨钿甜成人礼现场照
        img_adult_ceremony = self._create_image(
            asset_name="黄杨钿甜成人礼现场照.jpg",
            design_doc_height=4, aspect_ratio=1.78, position_xy=(-3.5, 0),
            name_key="adult_ceremony_photo", element_names=el_names
        )

        # 黄杨钿甜
        img_hyt_portrait = self._create_image(
            asset_name="image.character",  # 设计文档指定名称
            design_doc_height=4, aspect_ratio=0.75, position_xy=(4.5, 0),
            name_key="hyt_portrait", element_names=el_names
        )

        # "2025年5月"
        text_date = self._create_text(
            "2025年5月", font_size_px=24, color=BLACK, position_xy=(-6, 3.5),
            name_key="date_text", element_names=el_names
        )

        # "黄杨钿甜成人礼佩戴名贵耳环引争议"
        text_title1 = self._create_text(
            "黄杨钿甜成人礼佩戴名贵耳环引争议", font_size_px=32, color=BLACK, position_xy=(0, 3),
            name_key="title1_text", weight=BOLD, element_names=el_names
        )

        # --- 动画 (0.0s - 5.0s) ---
        # 0-2s: 图片缩放，文字淡入/滑入
        self.play(
            LaggedStart(
                AnimationGroup(
                    FadeIn(img_adult_ceremony, shift=RIGHT * 0.5),
                    img_adult_ceremony.animate.scale(1.05),  # 轻微缩放
                    run_time=1.5
                ),
                AnimationGroup(
                    FadeIn(img_hyt_portrait, shift=LEFT * 0.5),
                    img_hyt_portrait.animate.scale(1.05),  # 轻微放大
                    run_time=1.5
                ),
                FadeIn(text_date, shift=RIGHT),
                FadeIn(text_title1, shift=DOWN),
                lag_ratio=0.25
            ),
            run_time=2.0
        )

        LayoutManager.check_all_overlaps([
            img_adult_ceremony, img_hyt_portrait,
            text_date, text_title1
        ])
        self.wait(3.0)  # 保持状态 (2s + 3s = 5s)

    def play_scene_2(self, el_names):
        # --- 清理上一场景元素 ---
        # 获取上一场景的对象以便移除
        img_adult_ceremony = LayoutManager.get_object_by_name(el_names["adult_ceremony_photo"])
        img_hyt_portrait = LayoutManager.get_object_by_name(el_names["hyt_portrait"])
        text_date = LayoutManager.get_object_by_name(el_names["date_text"])
        text_title1 = LayoutManager.get_object_by_name(el_names["title1_text"])

        self.play(
            FadeOut(img_adult_ceremony),
            FadeOut(img_hyt_portrait),
            FadeOut(text_date),
            FadeOut(text_title1),
            run_time=0.5  # 快速淡出
        )
        current_time = 5.0 + 0.5  # 5.5s

        LayoutManager.remove_object(el_names["adult_ceremony_photo"])
        LayoutManager.remove_object(el_names["hyt_portrait"])
        LayoutManager.remove_object(el_names["date_text"])
        LayoutManager.remove_object(el_names["title1_text"])

        # --- 元素创建 (5.0s - 9.0s) ---
        # 黄杨钿甜佩戴的GRAFF耳环
        img_earring_prop = self._create_image(
            asset_name="image.prop",  # 设计文档指定名称
            design_doc_height=4.2, aspect_ratio=1.00, position_xy=(0, 0),
            name_key="earring_prop", element_names=el_names
        )
        img_earring_prop.set_opacity(0)  # Start invisible for animation

        # "疑似价值230万元格拉夫耳环"
        # Position relative to earring. Earring bottom is at -4.2/2 = -2.1.
        # So text at Y=-1 means its center is at Y=-1.
        # Design doc (0, -1) is absolute for text center.
        # Let's adjust relative to earring after creation.
        text_price = self._create_text(
            "疑似价值230万元格拉夫耳环", font_size_px=28, color=YELLOW, position_xy=(0, -2.5),  # Initial position guess
            name_key="price_text", weight=BOLD, element_names=el_names
        )
        # Precise positioning below earring
        safe_pos_price = LayoutManager.get_safe_position_below(img_earring_prop, text_price, margin=0.3)
        text_price.move_to(safe_pos_price)
        LayoutManager.ensure_screen_bounds(text_price, el_names["price_text"])
        text_price.set_opacity(0)

        # "(≈普通家庭30年收入)"
        text_income_comparison = self._create_text(
            "(≈普通家庭30年收入)", font_size_px=20, color=LIGHT_GRAY, position_xy=(0, -3.2),  # Initial position guess
            name_key="income_comparison_text", element_names=el_names
        )
        # Precise positioning below price text
        safe_pos_income = LayoutManager.get_safe_position_below(text_price, text_income_comparison, margin=0.1)
        text_income_comparison.move_to(safe_pos_income)
        LayoutManager.ensure_screen_bounds(text_income_comparison, el_names["income_comparison_text"])
        text_income_comparison.set_opacity(0)

        # --- 动画 (5.0s - 9.0s) ---
        # 耳环出现并放大 (0.7s)
        # 简化放大镜效果：耳环从成人礼照片中心（上一场景）位置出现并移动放大到屏幕中心
        origin_pos_for_earring = img_adult_ceremony.get_center() if img_adult_ceremony else ORIGIN

        self.play(
            img_earring_prop.copy().set_opacity(1).move_to(origin_pos_for_earring).scale(
                0.1)  # Dummy for transform source
            .animate.scale(10).move_to(img_earring_prop.get_center()).set_opacity(1),
            # End with the actual img_earring_prop fully visible
            # Using actual object directly for animation:
            # img_earring_prop.set_opacity(1).set_z_index(10),
            # img_earring_prop.animate.scale(1).move_to(img_earring_prop.get_center()),
            # This is simpler:
            FadeIn(img_earring_prop, scale=5),  # Start large and scale down to actual size. Or scale up
            run_time=0.7
        )
        current_time += 0.7  # 6.2s

        # 闪光效果 (6s in design, so 1s into this 5-9s segment)
        # current_time is now 6.2s, flash should have been at 6.0s.
        # Let's play it with the text appearances.

        # 价格文字滑入 (0.5s)
        self.play(
            Flash(img_earring_prop.get_center(), color=YELLOW, flash_radius=img_earring_prop.width, num_lines=20,
                  line_length=0.3, time_width=0.5),
            text_price.animate.set_opacity(1).shift(UP * 0.2),  # From slightly below its final pos
            run_time=0.5
        )
        current_time += 0.5  # 6.7s

        # 比较说明渐显 (0.5s)
        self.play(
            FadeIn(text_income_comparison, shift=UP * 0.1),
            run_time=0.5
        )
        current_time += 0.5  # 7.2s

        LayoutManager.check_all_overlaps([
            el_names["earring_prop"], el_names["price_text"], el_names["income_comparison_text"]
        ])
        self.wait(9.0 - current_time)  # 9.0 - 7.2 = 1.8s

    def play_scene_3(self, el_names):
        # --- 清理/转换上一场景元素 ---
        text_price = LayoutManager.get_object_by_name(el_names["price_text"])
        text_income_comparison = LayoutManager.get_object_by_name(el_names["income_comparison_text"])
        img_earring_prop = LayoutManager.get_object_by_name(el_names["earring_prop"])

        self.play(
            FadeOut(text_price),
            FadeOut(text_income_comparison),
            run_time=0.5
        )
        current_time = 9.0 + 0.5  # 9.5s
        LayoutManager.remove_object(el_names["price_text"])
        LayoutManager.remove_object(el_names["income_comparison_text"])

        # --- 元素创建与转换 (9.0s - 13.0s) ---
        # 耳环缩小并移至左上角
        new_earring_height = 1.5
        new_earring_width = new_earring_height * 1.0  # aspect_ratio = 1.0
        target_earring_pos = np.array([-5, 3, 0])

        # 网络热议截图
        img_hot_topic_screenshot = self._create_image(
            asset_name="网络热议截图.png",
            design_doc_height=4.5, aspect_ratio=1.78, position_xy=(2, 0),
            name_key="hot_topic_screenshot", element_names=el_names
        )
        img_hot_topic_screenshot.set_opacity(0)  # For animation

        # "网络热议"文字
        text_hot_topic = self._create_text(
            "网络热议", font_size_px=26, color=BLACK, position_xy=(2, 2),  # Above screenshot
            name_key="hot_topic_text", weight=BOLD, element_names=el_names
        )
        # Adjust based on screenshot
        safe_pos_hot_topic = LayoutManager.get_safe_position_above(img_hot_topic_screenshot, text_hot_topic, margin=0.2)
        text_hot_topic.move_to(safe_pos_hot_topic)
        LayoutManager.ensure_screen_bounds(text_hot_topic, el_names["hot_topic_text"])
        text_hot_topic.set_opacity(0)

        # 关键词
        text_kw_xuanfu = self._create_text("炫富", 24, RED, (1, -2), "keyword_xuanfu", el_names)  # Design: (1, -2)
        text_kw_shechi = self._create_text("奢侈", 24, BLUE, (2, -2), "keyword_shechi", el_names)  # Design: (2, -2)
        text_kw_jiajing = self._create_text("家境", 24, GREEN, (3, -2), "keyword_jiajing", el_names)  # Design: (3, -2)
        text_kw_zhiyi = self._create_text("质疑", 24, PURPLE, (4, -2), "keyword_zhiyi", el_names)  # Design: (4, -2)

        keywords_group = VGroup(text_kw_xuanfu, text_kw_shechi, text_kw_jiajing, text_kw_zhiyi).arrange(RIGHT, buff=0.5)
        # Reposition based on design doc individual coordinates (design uses absolute)
        # Or position group below screenshot
        safe_pos_keywords = LayoutManager.get_safe_position_below(img_hot_topic_screenshot, keywords_group, margin=0.3)
        # keywords_group.move_to(safe_pos_keywords) # This overrides specific X positions.
        # So, keep individual positions, ensure Y is reasonable below screenshot.
        # Let's try setting Y for all keywords based on being below screenshot.
        # Approximate Y for keywords: img_hot_topic_screenshot Y center (0) - height/2 (2.25) - margin = -2.5
        # Design uses Y = -2, which is fine.

        for kw in keywords_group: kw.set_opacity(0)

        # --- 动画 (9.0s - 13.0s) ---
        # 9.0s: 耳环变换，截图滑入, "网络热议"文字出现, "炫富"出现
        self.play(
            img_earring_prop.animate.set_height(new_earring_height, stretch=False).move_to(target_earring_pos),
            FadeIn(img_hot_topic_screenshot, shift=LEFT * 2),  # Slide from right
            FadeIn(text_hot_topic),
            GrowFromCenter(text_kw_xuanfu.set_opacity(1)),
            run_time=1.0  # This means kw_xuanfu appears fully at 10.0s (if current_time = 9.0 when this plays)
        )  # End time for this block based on scene timeline: 9.0s + 1.0s = 10.0s
        # Design wants keywords at 9.0, 9.5, 10.0, 10.5. This means start time of appearance.
        # Previous logic of calculating current_time was for sequential waits.
        # Let's use explicit waits for keyword timing.
        # The above self.play() takes 1s, so it finishes at scene time 10.0s (if started at 9.0s from previous FadeOut)
        # Let's assume FadeOut took 0s for calculation simplicity, or adjust.
        # Let's assume previous FadeOut finishes at 9.0s (total scene time).
        # This first play block:
        # self.play(..., run_time=0.3) # Show "炫富" quickly
        # self.wait(0.2) # Wait until 9.5s
        # self.play(GrowFromCenter(text_kw_shechi.set_opacity(1)), run_time=0.3)
        # ... this is better.

        # Corrected keyword timing:
        # Scene time is 9.0s after FadeOuts.
        # Earring move, screenshot slide, "网络热议" text (these can take longer, e.g. 1s)
        anim_duration_main_layout = 0.7
        self.play(
            img_earring_prop.animate.set_height(new_earring_height, stretch=False).move_to(target_earring_pos),
            FadeIn(img_hot_topic_screenshot, shift=LEFT * 2),
            FadeIn(text_hot_topic),
            run_time=anim_duration_main_layout
        )  # Ends at 9.0 + 0.7 = 9.7s

        # Keywords:
        # "炫富" at 9.0s - this means it should be in the above animation block.
        # Let's adjust.
        # The first block of animations for this scene (earring move, screenshot) starts at 9.0s.
        # "炫富" also appears at 9.0s.
        # This structure implies "炫富" appears during the main layout animation.

        # Play main layout changes (earring, screenshot, "网络热议" text)
        self.play(
            img_earring_prop.animate.set_height(new_earring_height, stretch=False).move_to(target_earring_pos),
            FadeIn(img_hot_topic_screenshot, shift=LEFT * 2),  # from right
            FadeIn(text_hot_topic, shift=UP * 0.2),
            # "炫富" (9.0s)
            Write(text_kw_xuanfu.set_opacity(1)),
            run_time=1.0  # Overall duration for this block, all start at 9.0s
        )
        # Scene time is now 9.0 + 1.0 = 10.0s

        # "奢侈" (9.5s) - Need to wait if 9.0 + 1.0 > 9.5. Here 10.0 > 9.5. So "奢侈" is delayed.
        # This means the description of keywords appearing *at* 9.0, 9.5, etc. is their animation *start*.
        # This implies a sequence of plays with waits.

        # Reset current_time for this segment
        current_scene_time = 9.0
        # First play (earring, screenshot, hot_topic_text). Let's say 0.8s.
        # "炫富" needs to start at 9.0s.
        self.play(
            AnimationGroup(
                img_earring_prop.animate.set_height(new_earring_height, stretch=False).move_to(target_earring_pos),
                FadeIn(img_hot_topic_screenshot, shift=LEFT * 2),
                FadeIn(text_hot_topic, shift=UP * 0.2),
            ),
            AnimationGroup(  # Starts at 9.0s
                Succession(Wait(0.0), Write(text_kw_xuanfu.set_opacity(1), run_time=0.4))  # "炫富" 9.0-9.4s
            ),
            run_time=0.8  # Main layout changes finish at 9.8s. "炫富" finishes at 9.4s.
        )
        current_scene_time = 9.0 + 0.8  # = 9.8s

        # "奢侈" (9.5s)
        wait_for_shechi = 9.5 - (9.0 + 0.0)  # Wait from start of this scene segment.
        # But main animation is 0.8s. "炫富" ends at 9.4s.
        # Let's simplify to sequential timed reveals. This matches "依次出现".
        # After main layout animation (ends 9.8s), reveal other keywords.
        # This deviates from exact 9.5, 10.0, 10.5 start times if main layout is long.
        # Alternative: use a single self.play with LaggedStart for keywords.

        # Let's go with design: "炫富" 9.0, "奢侈" 9.5, "家境" 10.0, "质疑" 10.5.
        # This means a complex timeline setup.
        # Simpler: All main changes finish, then keywords pop one by one.
        # The prompt mentions "弹出动画依次出现在截图下方, 分别在9.0秒、9.5秒、10.0秒和10.5秒"
        # This means the *start* of their animation.

        # Time: 9.0s
        self.play(  # This block defines animations starting at 9.0s
            # Main visual changes
            img_earring_prop.animate.set_height(new_earring_height, stretch=False).move_to(target_earring_pos),
            FadeIn(img_hot_topic_screenshot, shift=LEFT * 2),
            FadeIn(text_hot_topic, shift=UP * 0.2),
            # Keyword animations, timed with `Wait` within `Succession`
            Succession(  # 炫富 9.0s
                Write(text_kw_xuanfu.set_opacity(1), run_time=0.4),  # Finishes 9.4s
                Wait(9.5 - (9.0 + 0.4)),  # Wait until 9.5s. Wait(0.1s)
                Write(text_kw_shechi.set_opacity(1), run_time=0.4),  # 奢侈 9.5s-9.9s
                Wait(10.0 - (9.5 + 0.4)),  # Wait until 10.0s. Wait(0.1s)
                Write(text_kw_jiajing.set_opacity(1), run_time=0.4),  # 家境 10.0s-10.4s
                Wait(10.5 - (10.0 + 0.4)),  # Wait until 10.5s. Wait(0.1s)
                Write(text_kw_zhiyi.set_opacity(1), run_time=0.4)  # 质疑 10.5s-10.9s
            ),
            run_time=(10.5 + 0.4) - 9.0  # Total duration of this block = 1.9s
        )
        current_scene_time = 10.9  # Time when last keyword animation finishes.

        LayoutManager.check_all_overlaps([
            el_names["earring_prop"], el_names["hot_topic_screenshot"], el_names["hot_topic_text"],
            el_names["keyword_xuanfu"], el_names["keyword_shechi"], el_names["keyword_jiajing"],
            el_names["keyword_zhiyi"]
        ])
        self.wait(13.0 - current_scene_time)  # 13.0 - 10.9 = 2.1s

    def play_scene_4(self, el_names):
        # --- 清理上一场景元素 ---
        # These objects were created in play_scene_3
        img_earring_prop = LayoutManager.get_object_by_name(el_names["earring_prop"])
        img_hot_topic_screenshot = LayoutManager.get_object_by_name(el_names["hot_topic_screenshot"])
        text_hot_topic = LayoutManager.get_object_by_name(el_names["hot_topic_text"])
        text_kw_xuanfu = LayoutManager.get_object_by_name(el_names["keyword_xuanfu"])
        text_kw_shechi = LayoutManager.get_object_by_name(el_names["keyword_shechi"])
        text_kw_jiajing = LayoutManager.get_object_by_name(el_names["keyword_jiajing"])
        text_kw_zhiyi = LayoutManager.get_object_by_name(el_names["keyword_zhiyi"])

        elements_to_fade = [
            img_earring_prop, img_hot_topic_screenshot, text_hot_topic,
            text_kw_xuanfu, text_kw_shechi, text_kw_jiajing, text_kw_zhiyi
        ]
        self.play(
            *[FadeOut(obj) for obj in elements_to_fade if obj is not None],
            run_time=0.5  # 13.0s + 0.5s = 13.5s
        )
        for name_key in ["earring_prop", "hot_topic_screenshot", "hot_topic_text",
                         "keyword_xuanfu", "keyword_shechi", "keyword_jiajing", "keyword_zhiyi"]:
            LayoutManager.remove_object(el_names[name_key])

        current_scene_time = 13.0 + 0.5  # = 13.5s

        # --- 元素创建 (13.0s - 19.0s) ---
        # 黄杨钿甜 (缩小版)
        img_hyt_portrait_small = self._create_image(
            asset_name="image.character",  # Using the same asset as before
            design_doc_height=3.3, aspect_ratio=0.75, position_xy=(-4.5, 0),
            name_key="hyt_portrait", element_names=el_names  # Reuse name if it's the same conceptual element
        )
        img_hyt_portrait_small.set_opacity(0)

        # "'向妈妈借的'"
        text_quote = self._create_text(
            "'向妈妈借的'", font_size_px=30, color=BLACK, position_xy=(0, 2),
            name_key="quote_text", element_names=el_names  # Italic not directly supported by Text, use font variant if available or ignore
        )
        text_quote.set_opacity(0)

        # "从炫富质疑到财富来源争议"
        text_title2 = self._create_text(
            "从炫富质疑到财富来源争议", font_size_px=32, color=GRAY, position_xy=(0, -3),
            name_key="title2_text", weight=BOLD, element_names=el_names
        )
        text_title2.set_opacity(0)

        # --- 动画 (13.0s - 19.0s) ---
        # 黄杨钿甜照片出现 (13.5s - 14.0s based on current_scene_time)
        # "'向妈妈借的'"文字中央出现 (13.0s - 13.5s) -> needs to be earlier or simultaneous
        # Let's make these appear together
        self.play(
            FadeIn(img_hyt_portrait_small, shift=RIGHT * 0.5),
            FadeIn(text_quote, scale=0.8),
            run_time=0.5  # Ends at 13.5 + 0.5 = 14.0s
        )
        current_scene_time = 14.0

        # "'向妈妈借的'" 放大变红 (设计: 13-14秒, current_scene_time already 14.0, so do it now)
        self.play(
            text_quote.animate.scale(1.1).set_color(RED),
            run_time=1.0  # Ends at 14.0 + 1.0 = 15.0s
        )
        current_scene_time = 15.0

        # "从炫富质疑到财富来源争议" 出现并变色 (15.0s)
        self.play(
            GrowFromCenter(text_title2.set_opacity(1)),  # Starts small, grows to full size
            run_time=1.0  # Ends at 15.0 + 1.0 = 16.0s
        )
        current_scene_time = 16.0
        self.play(
            text_title2.animate.set_color(RED),  # Becomes醒目红色
            run_time=0.5  # Ends at 16.0 + 0.5 = 16.5s
        )
        current_scene_time = 16.5

        LayoutManager.check_all_overlaps([
            el_names["hyt_portrait"], el_names["quote_text"], el_names["title2_text"]
        ])
        self.wait(19.0 - current_scene_time)  # 19.0 - 16.5 = 2.5s