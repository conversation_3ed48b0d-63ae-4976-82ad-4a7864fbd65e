<svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- 背景 -->
    <rect width="1920" height="1080" fill="#FFFFFF" />

    <!-- 定义滤镜和渐变 -->
    <defs>
        <!-- 半透明蓝色背景 -->
        <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#3498db;stop-opacity:0.7" />
            <stop offset="100%" style="stop-color:#2980b9;stop-opacity:0.7" />
        </linearGradient>

        <!-- 分割线渐变 -->
        <linearGradient id="dividerGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:#e0e0e0;stop-opacity:0" />
            <stop offset="50%" style="stop-color:#a0a0a0;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#e0e0e0;stop-opacity:0" />
        </linearGradient>
    </defs>

    <!-- 3. 主要组成部分 -->

    <!-- 3.1 人物元素 - 黄杨钿甜 (初始位置在画面外) -->
    <image id="huangYangDianTian" xlink:href="黄杨钿甜.jpg" width="640" height="853" x="-320" y="113.5" opacity="0">
        <animate id="huangYangDianTian_fadeIn" attributeName="opacity" from="0" to="1" begin="0s" dur="1s" fill="freeze" />
        <animate id="huangYangDianTian_moveIn" attributeName="x" from="-320" to="0" begin="0s" dur="1s" fill="freeze" calcMode="spline" keySplines="0 0.5 0.5 1" />
        <animate id="huangYangDianTian_fadeOut" attributeName="opacity" from="1" to="0" begin="3s" dur="0.5s" fill="freeze" />

        <!-- 最终场景再次出现 -->
        <animate id="huangYangDianTian_finalFadeIn" attributeName="opacity" from="0" to="1" begin="10s" dur="1.5s" fill="freeze" />
        <animate id="huangYangDianTian_finalMove" attributeName="x" from="-320" to="1120" begin="10s" dur="1.5s" fill="freeze" calcMode="spline" keySplines="0 0.5 0.5 1" />
    </image>

    <!-- 3.2 道具元素 - GRAFF耳环 -->
    <image id="graffEarring" xlink:href="黄杨钿甜佩戴的graff耳环.jpg" width="400" height="400" x="120" y="150" opacity="0">
        <!-- 从黄杨钿甜耳朵位置放大出现 -->
        <animate id="earring_fadeIn" attributeName="opacity" from="0" to="1" begin="3s" dur="1.5s" fill="freeze" />
        <animate id="earring_scaleX" attributeName="width" from="40" to="400" begin="3s" dur="1.5s" fill="freeze" calcMode="spline" keySplines="0 0.5 0.5 1" />
        <animate id="earring_scaleY" attributeName="height" from="40" to="400" begin="3s" dur="1.5s" fill="freeze" calcMode="spline" keySplines="0 0.5 0.5 1" />
        <animate id="earring_moveX" attributeName="x" from="320" to="760" begin="3s" dur="1.5s" fill="freeze" calcMode="spline" keySplines="0 0.5 0.5 1" />
        <animate id="earring_moveY" attributeName="y" from="350" to="340" begin="3s" dur="1.5s" fill="freeze" calcMode="spline" keySplines="0 0.5 0.5 1" />

        <!-- 缩小移至右上角 -->
        <animate id="earring_moveToCornerX" attributeName="x" from="760" to="1400" begin="7s" dur="1.5s" fill="freeze" calcMode="spline" keySplines="0.25 0.1 0.25 1" />
        <animate id="earring_moveToCornerY" attributeName="y" from="340" to="70" begin="7s" dur="1.5s" fill="freeze" calcMode="spline" keySplines="0.25 0.1 0.25 1" />
        <animate id="earring_scaleDownX" attributeName="width" from="400" to="200" begin="7s" dur="1.5s" fill="freeze" calcMode="spline" keySplines="0.25 0.1 0.25 1" />
        <animate id="earring_scaleDownY" attributeName="height" from="400" to="200" begin="7s" dur="1.5s" fill="freeze" calcMode="spline" keySplines="0.25 0.1 0.25 1" />
    </image>

    <!-- GRAFF品牌logo -->
    <image id="graffLogo" xlink:href="GRAFF品牌logo.png" width="300" height="100" x="810" y="640" opacity="0">
        <animate id="logo_fadeIn" attributeName="opacity" from="0" to="1" begin="4.5s" dur="1s" fill="freeze" calcMode="spline" keySplines="0.25 0.1 0.25 1" />
        <animate id="logo_fadeOut" attributeName="opacity" from="1" to="0" begin="7s" dur="0.5s" fill="freeze" />
    </image>

    <!-- 社交媒体评论截图 -->
    <image id="socialComments" xlink:href="社交媒体评论截图.png" width="1600" height="360" x="160" y="720" opacity="0">
        <animate id="comments_fadeIn" attributeName="opacity" from="0" to="1" begin="11.5s" dur="1s" fill="freeze" calcMode="spline" keySplines="0 0 0.58 1" />
    </image>

    <!-- 分割线 -->
    <rect id="divider" x="958" y="180" width="4" height="720" fill="url(#dividerGradient)" opacity="0">
        <animate id="divider_fadeIn" attributeName="opacity" from="0" to="1" begin="10s" dur="1.5s" fill="freeze" />
    </rect>

    <!-- 3.4 文字关键词元素 -->

    <!-- "2025年5月 成人礼" 文字 -->
    <g id="dateText">
        <!-- 背景条 -->
        <rect x="1290" y="80" width="300" height="40" rx="5" ry="5" fill="url(#blueGradient)" opacity="0">
            <animate attributeName="opacity" from="0" to="0.8" begin="1s" dur="0.5s" fill="freeze" />
            <animate attributeName="opacity" from="0.8" to="0" begin="3s" dur="0.5s" fill="freeze" />
        </rect>

        <!-- 文字 - 使用打字机效果 -->
        <text x="1440" y="108" font-family="Arial, Helvetica, sans-serif" font-size="32px" fill="#FFFFFF" text-anchor="middle">
            <animate id="dateText_typing" attributeName="textLength" from="0" to="280" begin="1s" dur="1s" fill="freeze" />
            <animate attributeName="opacity" from="1" to="0" begin="3s" dur="0.5s" fill="freeze" />
            2025年5月 成人礼
        </text>
    </g>

    <!-- "据鉴定为格拉夫(GRAFF)祖母绿钻石耳环" 文字 -->
    <text id="earringDesc" x="960" y="200" font-family="Arial, Helvetica, sans-serif" font-size="36px" fill="#000000" text-anchor="middle" opacity="0">
        据鉴定为<tspan font-style="italic">"格拉夫(GRAFF)"</tspan>祖母绿钻石耳环
        <animate attributeName="opacity" from="0" to="1" begin="4s" dur="1s" fill="freeze" />
        <animate attributeName="opacity" from="1" to="0" begin="7s" dur="0.5s" fill="freeze" />
    </text>

    <!-- "¥2,300,000" 价格数字 -->
    <text id="priceNumber" x="700" y="540" font-family="Arial, Helvetica, sans-serif" font-size="72px" fill="#FF0000" text-anchor="middle" opacity="0">
        <animate attributeName="opacity" from="0" to="1" begin="7s" dur="0.2s" fill="freeze" />
        <animate attributeName="opacity" from="1" to="0" begin="10s" dur="0.5s" fill="freeze" />
        <tspan id="priceValue">¥0</tspan>
    </text>

    <!-- "市场公价约230万元" 文字 -->
    <text id="priceDesc" x="700" y="640" font-family="Arial, Helvetica, sans-serif" font-size="32px" fill="#000000" text-anchor="middle" opacity="0">
        市场公价约230万元
        <animate attributeName="opacity" from="0" to="1" begin="8s" dur="0.5s" fill="freeze" />
        <animate attributeName="opacity" from="1" to="0" begin="10s" dur="0.5s" fill="freeze" />
    </text>

    <!-- "配饰与公众形象的巨大反差，引发网友广泛质疑" 文字 -->
    <text id="conclusionText" x="960" y="200" font-family="Arial, Helvetica, sans-serif" font-size="36px" fill="#000000" text-anchor="middle" opacity="0">
        配饰与公众形象的巨大反差，引发网友广泛质疑
        <animate attributeName="opacity" from="0" to="1" begin="11s" dur="1s" fill="freeze" />
    </text>

    <!-- 使用SMIL动画实现价格计数 -->
    <script type="text/javascript">
        <![CDATA[
        // 等待SVG加载完成
        window.addEventListener('load', function() {
            // 获取价格元素
            var priceElement = document.getElementById('priceValue');

            // 定义价格变化数组
            var priceValues = [
                "¥0",
                "¥230,000",
                "¥460,000",
                "¥690,000",
                "¥1,150,000",
                "¥1,380,000",
                "¥1,610,000",
                "¥1,840,000",
                "¥2,070,000",
                "¥2,300,000"
            ];

            // 设置初始值
            priceElement.textContent = priceValues[0];

            // 设置计时器，在7秒后开始动画
            setTimeout(function() {
                var index = 0;
                var interval = setInterval(function() {
                    index++;
                    if (index < priceValues.length) {
                        priceElement.textContent = priceValues[index];
                    } else {
                        clearInterval(interval);
                    }
                }, 200); // 每200毫秒更新一次，总共9次变化，持续1.8秒
            }, 7000);
        });
        ]]>
    </script>
</svg>