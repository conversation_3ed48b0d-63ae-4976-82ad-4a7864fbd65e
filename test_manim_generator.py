#!/usr/bin/env python3
"""
测试 ManimGenerator 的基本功能
"""

from utils.manim_utils import ManimUtils

def test_extract_manim_code():
    """测试代码提取功能"""
    
    # 测试用例1: ```python 代码块
    test_text1 = """
    这是一些说明文字
    
    ```python
    from manim import *
    
    class TestScene(Scene):
        def construct(self):
            circle = Circle()
            self.play(Create(circle))
    ```
    
    这是结尾文字
    """
    
    result1 = ManimUtils.extract_manim_code(test_text1)
    print("测试用例1 (```python):")
    print(result1)
    print("-" * 50)
    
    # 测试用例2: ``` 代码块（无语言标识）
    test_text2 = """
    这是一些说明文字
    
    ```
    from manim import *
    
    class TestScene(Scene):
        def construct(self):
            circle = Circle()
            self.play(Create(circle))
    ```
    
    这是结尾文字
    """
    
    result2 = ManimUtils.extract_manim_code(test_text2)
    print("测试用例2 (```):")
    print(result2)
    print("-" * 50)
    
    # 测试用例3: 直接包含代码（无代码块标记）
    test_text3 = """
    这是一些说明文字
    
    from manim import *
    
    class TestScene(Scene):
        def construct(self):
            circle = Circle()
            self.play(Create(circle))
    """
    
    result3 = ManimUtils.extract_manim_code(test_text3)
    print("测试用例3 (直接代码):")
    print(result3)
    print("-" * 50)

if __name__ == "__main__":
    test_extract_manim_code()
