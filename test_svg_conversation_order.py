import asyncio
import sys
import os

# 将项目根目录添加到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from llm.ai_conversation import AIConversation

async def test_conversation_order():
    """测试对话顺序：审查员 → 修改师 → 审查员"""
    
    # SVG审查员系统提示词（简化版）
    reviewer_prompt = """
你是一个SVG代码审查员。检查SVG代码与prompt的一致性。
如果完全一致，回复"完全一致"。
如果有问题，列出[核心不一致]和[次要不一致]。
"""
    
    # SVG修改师系统提示词（简化版）
    modifier_prompt = """
你是一个SVG代码修改师。
根据审查员的反馈修改SVG代码。
输出修改后的完整SVG代码。
"""
    
    # 创建AI对话
    conversation = AIConversation(
        ai1_name="SVG代码审查员",
        ai2_name="SVG代码修改师", 
        ai1_character=reviewer_prompt,
        ai2_character=modifier_prompt,
        max_rounds=3,
        ai1_temperature=0.0,
        ai2_temperature=0.0
    )
    
    # 初始消息
    initial_message = """
**Prompt**: 创建一个显示绿色圆圈的SVG
**SVG代码**: <svg><circle fill="blue" r="50"/></svg>
"""
    
    print("开始测试对话顺序...")
    print("=" * 50)
    
    # 开始对话
    conversation_log = await conversation.start_conversation(
        initial_message=initial_message,
        max_rounds=3,
        end_phrase="完全一致"
    )
    
    # 打印对话记录
    print("对话记录：")
    for i, entry in enumerate(conversation_log):
        role = entry.get('role', '')
        content = entry.get('content', '')[:100] + "..." if len(entry.get('content', '')) > 100 else entry.get('content', '')
        print(f"{i+1}. {role}: {content}")
        print("-" * 30)
    
    # 验证对话顺序
    expected_order = ["user", "SVG代码审查员", "SVG代码修改师", "SVG代码审查员"]
    actual_order = [entry.get('role', '') for entry in conversation_log]
    
    print(f"\n预期顺序: {expected_order}")
    print(f"实际顺序: {actual_order}")
    
    if actual_order[:len(expected_order)] == expected_order:
        print("✅ 对话顺序正确：审查员 → 修改师 → 审查员")
    else:
        print("❌ 对话顺序错误")
    
    return conversation_log

if __name__ == "__main__":
    asyncio.run(test_conversation_order()) 