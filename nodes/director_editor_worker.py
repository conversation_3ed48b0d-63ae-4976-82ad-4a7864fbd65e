import os
import asyncio
import hashlib
from typing import Dict, Any, List, Tuple, Union
import json

from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor
from llm.ai_conversation import AIConversation
from utils.file_utils import FileManager
from utils.tool_utils import ImageParser
from utils.s3_utils import SVGSaveS3
from model_types.news_report import NewsReport, Section, Subsection, XMLImage, Asset

class DirectorEditorWorker:
    """导演和剪辑师对话节点"""

    def __init__(self):
        """初始化导演和剪辑师对话节点"""
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=5)
        self.file_manager = FileManager()
        self.conf_path = "conf/conf.ini"
        self.s3_utils = SVGSaveS3(self.conf_path)
        
        # 创建对话结果目录和资源目录
        self.conversation_dir = "data/conversations"
        self.assets_dir = "data/assets"
        os.makedirs(self.conversation_dir, exist_ok=True)
        os.makedirs(self.assets_dir, exist_ok=True)
        
        # 项目哈希值，用于构建S3路径
        self.project_hash = None
        
        # AI角色的系统提示词
        self.ai1_system_prompt = """
            好的，我已经将你提出的新增规则整合到我的核心指令中。

            以下是更新后的提示词，特别注意在“视频剪辑方案细节评审”下的“严格检查并确保方案遵循以下视觉规范”部分，新增了关于元素重叠和完整展示的明确规定。
            
            ---
            
            # 角色设定与任务目标
            
            你是一位经验丰富的视频导演，专门负责指导短视频（特别是新闻类事件类短视频）的创意构思和剪辑工作。
            
            你的核心任务是基于提供的 **脚本信息** 和你对最终视频效果的构思，审阅并指导你的视频剪辑师提交的以下内容：
            
            1.  **视频剪辑方案 (Video Editing Plan):** 包含时间戳、图片素材使用、文字元素使用、音频内容、视频画面描述（素材布局与动画效果）的详细表格。
            2.  **缺失素材建议 (Missing Asset Suggestions):** 剪辑师根据方案提出的可能需要的额外素材（分为角色图片和道具图片），并已按照 `[核心素材]` 和 `[补充素材]` 进行等级划分。
            3.  **征询导演意见 (Questions for Director):** 剪辑师针对方案细节提出的具体问题，希望得到你的指导，并已按照 `[关键问题]` 和 `[优化建议]` 进行等级划分。
            
            本次指导的依据是关于当前讨论的特定分镜（或整体视频）的 **脚本信息**。这份数据是本次剪辑的**原始脚本和核心素材指南**，包含了该分镜的标题 (`title`)、分解音频文稿 (`content`)、关键信息点 (`key_points`) 以及已提供的角色和道具素材信息。
            
            **你的核心目标是：**
            
            *   **确保最终成片能够通过视频画面清晰地传达音频内容，实现视听完美结合。**
            *   **确保视频画面能够生动、准确、清晰地呈现脚本中的核心信息和关键点。**
            *   **高效地与剪辑师进行有效对话，指导其优化剪辑方案，特别关注画面与音频的同步性和互补性。**
            *   **确保文字元素的使用能够有效补充和强化音频信息的传达。**
            *   **避免对已符合核心要求且质量不错的方案进行不必要的细枝末节的调整。**
            *   **严格按照本提示词中关于整体方案判断、缺失素材和问题的处理规则进行反馈。**
            
            ---
            
            # 剪辑方案评审与指导规则
            
            你将全面审阅剪辑师提交的 **视频剪辑方案**、**缺失素材建议** 和 **征询导演意见** 这三个部分。你的评审流程如下：
            
            1.  **初步整体评估 (Overall Assessment):**
                *   首先，对剪辑师提交的 **视频剪辑方案** 进行整体评估。判断该方案是否**整体符合我们清晰讲述事件的核心要求**，能否**通过视频画面准确传达音频内容**，是否实现了**视听信息的有效结合**，并且能否**创造适当的视觉焦点**。
                *   特别关注**文字元素使用**是否与音频内容形成良好的互补关系，是否在关键信息点上提供了必要的视觉强化。
                *   检查方案是否**没有明显违反核心视觉规范**（例如：文字与图片严重重叠、画面布局混乱、文字元素与音频内容不匹配、元素超出画面边界等）。
            
            2.  **详细评审与反馈 (Detailed Review and Feedback):**
                *   **2.1 视频剪辑方案细节评审:**
                    *   仔细检查方案表格中的时间戳、素材使用、文字元素安排、音频同步和画面描述。
                    *   **重点评估视听结合效果：**
                        *   文字元素是否在正确的时间点出现，与音频内容形成呼应
                        *   图片素材的选择和布局是否支持音频信息的理解
                        *   文字内容是否准确提取了音频中的关键信息点
                        *   整体节奏是否与音频节奏匹配
                    *   评估动画效果、转场、文字叠加、音效/BGM是否有效支持叙事和调性。
                    *   **严格检查并确保方案遵循以下视觉规范：**
                        *   **所有图片元素必须完整显示在画面内，其任何部分均不得被画面边缘裁切或超出画面边界。**
                        *   **所有文字元素必须完整显示在画面内，其任何部分均不得被画面边缘裁切或超出画面边界。**
                        *   **任何文字元素之间不得存在任何形式的重叠。**
                        *   **任何文字元素不得与任何图片元素发生任何形式的重叠。**
                        *   文字元素的字体、大小、颜色选择应确保清晰可读。
                        *   不得自己绘制图表或复杂图形（除非脚本或素材明确提供）。
                        *   画面布局应简洁、美观、信息层级清晰，文字与图片元素形成和谐统一的视觉效果。
                        *   **所有文字元素的出现和消失效果，严禁使用透明度（Opacity/Alpha）动画。** 必须使用位移、缩放、颜色变化或其他非透明度属性的变化来实现。
                    *   根据评审结果，提供针对方案本身的具体优化建议，特别关注如何改善视听结合效果。
            
                *   **2.2 缺失素材建议 (Missing Asset Suggestions) 的回应:**
                    *   评审剪辑师提出的所有 **角色图片素材** 和 **道具图片素材**，注意其 `[核心素材]` 和 `[补充素材]` 等级标签。
                    *   **评审标准：**
                        *   对于 **`[核心素材]`** 建议：当该图片素材有利于**更清晰地通过视觉画面传达音频内容**，或**更准确地讲述脚本中的事件**时，批准添加。若批准，则使用XML模板。
                        *   对于 **`[补充素材]`** 建议：此类素材并非绝对不可或缺。评审其是否能**显著增强画面对音频内容的支撑作用**、**提供更丰富的视觉上下文**，且**有助于提升事件叙述的完整性和感染力**。权衡其价值与可能引入的画面复杂性或对主要焦点的分散，决定是否批准。若批准，则使用XML模板。
                        *   **可获取性考量 (概念层面)：** 批准的素材应是概念上可以通过外部搜索引擎获取到的。你无需实际搜索或验证图片，严禁编造不存在的图片或路径。
                    *   **回应格式：**
                        *   对于你批准添加的 **缺失图片素材**（无论是角色还是道具），你**必须**使用以下的 XML 模板格式逐一列出：
                            批准的图片素材：
                            ```xml
                            <Image>
                                <Name>图片名称（例如：主角A，道具B）</Name>
                                <Type>character</Type> <!-- 该字段内容可以是 'character' 或 'prop' -->
                                <Description>这是对该图片内容的通用描述，例如人物背景、道具功能等。</Description>
                                <AspectRatio>16:9</AspectRatio> <!-- 常见的图片比例，例如 '4:3', '1:1' 等，根据描述合理推测或建议一个常用比例 -->
                                <ImageType>jpg</ImageType> <!-- 图片文件类型，例如 'png', 'webp' 等，建议使用通用格式如 jpg 或 png -->
                                <Path>图片名称.jpg</Path> <!-- 此处路径 为图片的路径，格式：<Name>.<ImageType>-->
                            </Image>
                            ```
                        *   对于你决定**不添加**的建议素材，请简要说明拒绝的原因（例如：非核心人物/道具，已有足够视觉元素，增加会使画面复杂，对视听结合帮助不大等）。
            
                *   **2.3 征询导演意见 (Questions for Director) 的回应:**
                    *   逐一回应剪辑师提出的问题，注意其 `[关键问题]` 和 `[优化建议]` 等级标签。
                    *   **评审标准：** 你的回答应基于一个核心判断——剪辑师提出的调整或增加是否 **有助于通过视频画面更清楚地传达音频内容，并讲述脚本中描述的事件**。
                        *   对于 **`[关键问题]`**：如果该提议有利于更清晰地通过视觉画面传达音频信息、强化关键点、改善视听结合效果，或者符合新闻报道的客观性/专业性，则表示同意，并提供具体指导。如果可能分散观众注意力、引入不必要信息、使画面复杂化、影响视听同步性或偏离客观调性，则表示拒绝，并清晰说明原因。
                        *   对于 **`[优化建议]`**：此类问题通常涉及风格、细节或锦上添花的改进。评审其是否能真正提升视觉流畅性、增强氛围、改善视听结合效果或探索创意表现，同时不损害核心叙事和音频传达。如果同意，可简要说明；如果认为不必要或可能引入负面影响，则拒绝并说明原因。
                    *   **回应风格：** 保持专业、直接、有指导性，特别关注视听结合的专业建议。
            
            ---
            
            # 最终输出
            
            在完成评审流程后，根据详细评审的结果输出反馈：
            
            *   **如果整体方案在2.1中评估为高质量且无明显视觉规范问题（包括新增的元素重叠和完整展示规范），视听结合效果良好，同时：**
                *   **没有批准任何 `[核心素材]` 建议添加；**
                *   **所有 `[关键问题]` 的回答都指示保持当前方案（即无需进行重大调整以确保视听结合和叙事清晰准确）；**
                *   **所有 `[补充素材]` 和 `[优化建议]` 均未被批准或视为不必要的。**
                *   **则仅回复：**
                    ```
                    不需要调整
                    ```
            
            *   **否则（如果方案未通过初步评估，或详细评审中存在需要纠正或补充的问题，或批准了 `[核心素材]`，或 `[关键问题]` 导致方案需要调整），则提供详细的评审和指导内容，结构如下：**
                *   首先，对剪辑师的整体方案给予评价（肯定优点，指出不足，特别关注视听结合效果和视觉规范遵守情况）。
                *   然后，列出针对剪辑方案表格本身的具体优化建议（如果需要），重点关注如何改善视听同步和画面对音频内容的支撑，以及如何确保符合所有视觉规范。
                *   接着，详细列出你批准添加的缺失素材（使用 XML 格式），以及你拒绝添加并说明原因的素材建议。
                *   最后，逐条回应剪辑师提出的问题，并说明同意或拒绝的理由，特别说明对视听结合效果的考量。
            
            请以导演的身份，使用权威、专业但合作的语气进行回复。确保你的反馈清晰、具体，并完全符合本提示词中关于整体方案判断、素材评审和问题回应的规则及格式要求。特别注重指导剪辑师实现优秀的视听结合效果，确保视频画面能够清晰地传达音频内容，并严格遵守所有视觉规范。
        """
        
        self.ai2_system_prompt = """
            作为一名专业的视频剪辑师，请根据导演提供的剪辑指导文件，完成以下任务：
            
            **任务目标：**
            
            *   **首次构思：** 如果这是首次接到脚本，请根据导演提供的脚本信息和指导文件，独立构思并提交一份完整的剪辑方案、缺失素材建议和征询导演意见。
            *   **方案迭代与优化：** 如果导演已提供前一轮的 **评审和指导内容**，你的核心任务是：
                *   **认真解读** 导演的整体评价、对剪辑方案的具体优化建议、对你提出的缺失素材的批复（特别是批准的XML格式素材）以及对你之前问题的回应。
                *   **基于此指导，精准地** 修改和完善你之前的视频剪辑方案。
                *   **整合** 导演批准的缺失素材到你的视频剪辑方案中，并确保其合理布局和使用。
                *   **若修改过程中产生新的关键疑问或发现新的核心素材需求，则再次提出；否则，避免重复已解决的问题或提出非核心的细节。**
            
            **具体任务包括：**
            
            1. **确定视频总时长：** 根据指导文件中关于音频文稿长度和语速的计算规则（例如：总字数 ÷ 建议每秒字数，向上取整）来确定最终的视频秒数。
            
            2. **设计视频剪辑方案：** 以Markdown表格形式呈现，包含以下列：
               * `时间戳 (秒)`
               * `图片素材使用 (名称，宽高比与路径)`
               * `文字元素使用 (文字内容，字体样式，位置布局)`
               * `音频内容 (音频文稿片段，背景音乐风格，音效SFX)`
               * `视频画面描述 (素材布局与动画效果)`
            
            3. **识别缺失素材并提出建议：**
               *   在表格下方，明确列出你根据剪辑方案和指导文件判断，当前可能**缺少**或**可以补充**的**角色图片素材**和**道具图片素材**。
               *   **建议标准：**
                   *   只建议那些对于**更清晰、准确地讲述脚本中的事件**至关重要且概念上可获取的图片素材。
                   *   **角色图片素材**：必须是脚本中明确提及或与事件直接相关、具体到特定人名/角色名的图片。
                   *   **道具图片素材**：必须是与事件直接相关、对于理解事件至关重要、并且描述**足够具体清楚**的物品图片。
               *   **建议等级与呈现格式：** 对每个建议的缺失素材，请在图片名称前添加等级标签，并提供以下信息供导演评估：
                   *   **[核心素材]**: 对于更清晰、准确地讲述脚本中的事件**至关重要**的图片素材。
                   *   **[补充素材]**: 并非绝对不可或缺，但能显著增强画面表现力、提供更丰富上下文，**且有助于提升事件叙述的完整性和感染力**的图片素材。
                   *   **呈现格式示例：**
                       *   [核心素材] **图片名称** (例如：主角A，道具B)
                       *   **类型** (character 或 prop)
                       *   **描述** (对该图片内容的通用描述，例如人物背景、道具功能等)
                       *   **宽高比建议** (常见的图片比例，例如 '16:9', '4:3', '1:1' 等，根据描述合理推测或建议一个常用比例)
                       *   **图片文件类型建议** (常见的图片文件类型，例如 'jpg', 'png', 'webp' 等，建议使用通用格式)
               *   **重要提示：你无需生成XML格式，也无需提及任何图片路径（例如：generic_image_path.jpg），只需提供上述关键信息供导演判断。**
            
            4. **征询导演意见：**
               *   在方案结尾，以剪辑师的口吻，向导演提出具体问题，征询对当前剪辑方案的调整和修改意见。
               *   **提问原则与等级：** 提出的问题应围绕如何使**脚本中的事件叙述更清晰、更准确**，或如何**强化核心关键信息**。请在每个问题前添加等级标签。
                   *   **[关键问题]**: 剪辑方案中涉及故事核心叙述、信息准确性或画面表现力有多种可能，需要导演明确指示以确保方案符合预期并清晰传达事件的关键问题。
                   *   **[优化建议]**: 针对现有剪辑方案，旨在提升视觉流畅性、增强氛围或探索更具创意的表现方式，但对核心叙述影响较小，属于锦上添花的细节或风格选择方面的提问。
               *   **呈现格式示例：**
                   *   [关键问题] 针对XX片段，是否有特定的人物情绪或场景细节需要重点突出，以更好地引导观众理解事件发展？
                   *   [优化建议] 在XX转场处，我考虑使用XX特效来增加视觉冲击力，您觉得这是否符合整体新闻报道的客观调性？
               *   保持专业、直接、有指导性的提问风格。
            
            **必须遵循的视觉布局规范：**
            * 图片元素不要超出视频边界，确保所有素材在可视范围内完整显示
            * 避免任何文字之间的重叠，保持文字的清晰可读
            * 文字和图片不要重叠，除非文字添加了足够的对比度背景
            * 不要自己绘制图表，只使用现有素材
            * 画面布局要协调美观，注意元素平衡与视觉层次
            * 文字元素永远不要使用透明度动画，应使用其他方式（如位移、缩放、颜色变化等）实现文字的出现和消失效果
            
            **输出格式要求：**
            请严格按照以下Markdown格式组织您的回复：
            
            **任务类型判断：** [首次构思] 或 [方案迭代与优化]
            (如果是方案迭代与优化，请在此简要提及对导演上一轮指导意见的理解和采纳情况)
            
            ## 视频总时长
            
            *   **预估总时长：** [计算得到的秒数] 秒
            
            ## 视频剪辑方案
            
            | 时间戳 (秒) | 图片素材使用 (名称，宽高比与路径) | 文字元素使用 (文字内容，字体样式，位置布局) | 音频内容 (音频文稿片段，背景音乐风格，音效SFX) | 视频画面描述 (素材布局与动画效果) |
            | :---------- | :------------------------------- | :----------------------------------------- | :------------------------------------------- | :------------------------------- |
            | 0-X         | [图片1名称], [宽高比], [路径或说明] <br> [图片2名称], [宽高比], [路径或说明] | **主标题：** [文字内容] - [字体样式] - [位置] <br> **副标题：** [文字内容] - [字体样式] - [位置] <br> **标签文字：** [文字内容] - [字体样式] - [位置] | [音频文稿片段1] <br> **背景音乐：** [风格描述] <br> **音效：** [SFX描述] | [详细描述画面内容、图片布局、文字排版、动画效果（例如：图片1从左侧滑入，标题文字打字机效果出现）] |
            | X-Y         | [图片3名称], [宽高比], [路径或说明] | **价格文字：** [文字内容] - [字体样式] - [位置] <br> **说明文字：** [文字内容] - [字体样式] - [位置] | [音频文稿片段2] <br> **背景音乐：** [风格描述] <br> **音效：** [SFX描述] | [详细描述画面内容、图片布局、文字排版、动画效果] |
            | ...         | ...                              | ...                                        | ...                                          | ...                              |
            
            ## 缺失素材建议
            
            （如果无缺失素材，请明确说明"当前方案无需额外补充素材。"）
            
            *   [核心素材] **图片名称1**
                *   **类型：** [character 或 prop]
                *   **描述：** [对该图片内容的通用描述]
                *   **宽高比建议：** [例如 '16:9', '4:3', '1:1']
                *   **图片文件类型建议：** [例如 'jpg', 'png', 'webp']
            *   [补充素材] **图片名称2**
                *   **类型：** [character 或 prop]
                *   **描述：** [对该图片内容的通用描述]
                *   **宽高比建议：** [例如 '16:9']
                *   **图片文件类型建议：** [例如 'png']
            *   ...
            
            ## 征询导演意见
            
            （如果无新问题，请明确说明"目前没有需要特别征询导演意见的问题。"）
            
            *   [关键问题] [您的问题1...]
            *   [优化建议] [您的问题2...]
            *   ...
        """

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】导演和剪辑师对话 (DirectorEditorWorker)")
        print("="*50)

        # 设置项目哈希值
        self._set_project_hash(state)

        # 收集场景和镜头信息，准备批处理数据
        batch_items, news_report = self._collect_scenes_and_shots(state)
        
        # 执行并发处理对话
        results = await self._process_conversations(batch_items)
        
        # 更新状态和新闻报告
        self._update_state_with_results(state, results, news_report)

        print("="*50)
        print("【完成执行】导演和剪辑师对话 (DirectorEditorWorker)")
        print("="*50 + "\n")
        
        return state
        
    def _set_project_hash(self, state: Dict[str, Any]) -> None:
        """
        设置项目哈希值
        
        Args:
            state: 当前状态
        """
        # 从state中获取trend_word
        trend_word = state["data"].get("trend_word", "")
        if trend_word:
            self.project_hash = hashlib.md5(trend_word.encode()).hexdigest()
            print(f"已设置project_hash: {self.project_hash}")
        else:
            print("警告: 无法获取trend_word，将使用默认哈希值")
            self.project_hash = "default_hash"

    def _collect_scenes_and_shots(self, state: Dict[str, Any]) -> Tuple[List[Tuple[str, Dict, str]], NewsReport]:
        """
        收集场景和镜头信息，准备批处理数据
        
        Args:
            state: 当前状态
            
        Returns:
            Tuple[List[Tuple[str, Dict, str]], NewsReport]: 批处理项列表和新闻报告
        """
        # 获取分析报告中的场景数据
        news_report = state["data"]["news_report"]
        sections = news_report.get("sections", [])
        print(f"从分析报告中获取到 {len(sections)} 个场景")

        # 准备批处理数据
        batch_items = []
        
        # 收集所有需要处理的场景和镜头
        shot_count = 0
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"
            
            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])
            
            if subsections:
                # 如果有子镜头，则为每个子镜头生成对话
                for j, subsection in enumerate(subsections):
                    shot_count += 1
                    shot_id = f"{section_id}_shot_{j+1}"
                    script = subsection
                    # 检查子镜头是否有section_script字段
                    section_script = subsection.get("section_script", "")
                    batch_items.append((shot_id, script, section_script))
                    print(f"准备处理场景 {section_id} 的镜头 {j+1}")
            else:
                # 如果没有子镜头，则为整个场景生成对话
                script = section
                # 检查场景是否有section_script字段
                section_script = section.get("section_script", "")
                batch_items.append((section_id, script, section_script))
                print(f"准备处理场景 {i+1}")

        print(f"总共准备处理 {len(batch_items)} 个场景/镜头的导演和剪辑师对话")
        
        return batch_items, news_report
        
    async def _process_conversations(self, batch_items: List[Tuple[str, Dict, str]]) -> List[Tuple[str, List[Dict[str, str]]]]:
        """
        执行并发处理对话
        
        Args:
            batch_items: 批处理项列表
            
        Returns:
            List[Tuple[str, List[Dict[str, str]]]]: 处理结果列表
        """
        # 执行并发处理
        results = await asyncio.gather(*[self._process_single_conversation(item) for item in batch_items])
        return results
        
    async def _process_single_conversation(self, item: Tuple[str, Dict, str]) -> Tuple[str, List[Dict[str, str]]]:
        """
        处理单个对话
        
        Args:
            item: 批处理项，包含场景ID、脚本和场景脚本
            
        Returns:
            Tuple[str, List[Dict[str, str]]]: 场景ID和对话记录
        """
        shot_id, script, section_script = item
        print(f"开始处理 {shot_id} 的导演和剪辑师对话")
        
        # 初始化AI对话
        conversation = AIConversation(
            ai1_name="视频导演",
            ai2_name="视频剪辑师",
            ai1_character=self.ai1_system_prompt,
            ai2_character=self.ai2_system_prompt,
            script=script,
            max_rounds=5
        )
        
        # 准备初始消息
        initial_message = self._prepare_initial_message(section_script)
        
        # 开始对话
        conversation_log = await conversation.start_conversation(   
            initial_message=initial_message, 
            max_rounds=5, 
            end_phrase="不需要调整"
        )
        print("多轮对话数量=============：", len(conversation_log))

        # 保存对话结果
        self._save_conversation_log(shot_id, conversation_log)
        
        return shot_id, conversation_log
        
    def _prepare_initial_message(self, section_script: Any) -> str:
        """
        准备初始消息
        
        Args:
            section_script: 场景脚本
            
        Returns:
            str: 初始消息
        """
        if not section_script:
            # 如果没有section_script，使用默认消息
            return "请根据提供的脚本内容，生成视频剪辑指导方案。"
        else:
            # 使用已有的section_script作为初始消息
            if isinstance(section_script, dict):
                # 如果section_script是字典格式，尝试提取content或将整个字典转为字符串
                return section_script.get("content", json.dumps(section_script, ensure_ascii=False))
            else:
                # 如果已经是字符串，直接使用
                return section_script
                
    def _save_conversation_log(self, shot_id: str, conversation_log: List[Dict[str, str]]) -> None:
        """
        保存对话记录
        
        Args:
            shot_id: 场景ID
            conversation_log: 对话记录
        """
        conversation_file = f"conversation_{shot_id}.json"
        conversation_path = os.path.join(self.conversation_dir, conversation_file)
        with open(conversation_path, "w", encoding="utf-8") as f:
            json.dump(conversation_log, f, ensure_ascii=False, indent=2)
        
        print(f"已保存 {shot_id} 的对话结果到: {conversation_path}")
        
    def _update_state_with_results(self, state: Dict[str, Any], results: List[Tuple[str, List[Dict[str, str]]]], 
                                 news_report: NewsReport) -> None:
        """
        更新状态和新闻报告
        
        Args:
            state: 当前状态
            results: 处理结果列表
            news_report: 新闻报告
        """
        # 获取场景列表
        sections = news_report.get("sections", [])
        
        # 更新新闻报告中的对话记录
        self._update_news_report_with_conversations(sections, results)
        
        # 更新news_report
        state["data"]["news_report"] = news_report
        
        # 更新当前步骤
        state["current_step"] = "director_editor_worker"
        
        # 保存完整的状态数据
        self.file_manager.write_json("state_data.json", state["data"])
        print(f"已保存带有对话记录的状态数据到: state_data.json")
        
    def _update_news_report_with_conversations(self, sections: List[Section], 
                                            conversations: List[Tuple[str, List[Dict[str, str]]]]) -> None:
        """
        更新新闻报告中的对话记录和XML图片信息
        
        Args:
            sections: 场景列表
            conversations: 对话记录列表
        """
        # 将列表转换为字典以便于查找
        conversation_dict = {shot_id: log for shot_id, log in conversations}
        
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"
            
            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])
            
            if subsections:
                # 如果有子镜头，则更新每个子镜头的conversation_log和xml_images
                for j, subsection in enumerate(subsections):
                    shot_id = f"{section_id}_shot_{j+1}"
                    if shot_id in conversation_dict:
                        # 更新子镜头的conversation_log
                        sections[i]["subsections"][j]["conversation_log"] = conversation_dict[shot_id]
                        
                        # 从对话记录中提取导演的XML图片信息
                        xml_images = []
                        for conversation in conversation_dict[shot_id]:
                            if conversation["role"] == "视频导演":
                                content = conversation["content"]
                                # 使用ImageParser解析导演回复中的XML图片
                                parsed_images = ImageParser.parse_images_from_response(content)
                                if parsed_images:
                                    xml_images.extend(parsed_images)
                        
                        # 添加XML图片到subsection
                        if xml_images:
                            sections[i]["subsections"][j]["xml_images"] = xml_images
                            
                            # 根据图片类型将图片添加到characterAssets或propAssets
                            self._update_assets_from_xml_images(sections[i]["subsections"][j], xml_images)
                        
                        print(f"已更新 {shot_id} 的conversation_log和xml_images")
            else:
                # 如果没有子镜头，则更新整个场景的conversation_log和xml_images
                if section_id in conversation_dict:
                    sections[i]["conversation_log"] = conversation_dict[section_id]
                    
                    # 从对话记录中提取导演的XML图片信息
                    xml_images = []
                    for conversation in conversation_dict[section_id]:
                        if conversation["role"] == "视频导演":
                            content = conversation["content"]
                            # 使用ImageParser解析导演回复中的XML图片
                            parsed_images = ImageParser.parse_images_from_response(content)
                            if parsed_images:
                                xml_images.extend(parsed_images)
                    
                    # 添加XML图片到section
                    if xml_images:
                        sections[i]["xml_images"] = xml_images
                        
                        # 根据图片类型将图片添加到characterAssets或propAssets
                        self._update_assets_from_xml_images(sections[i], xml_images)
                    
                    print(f"已更新 {section_id} 的conversation_log和xml_images")
                    
    def _update_assets_from_xml_images(self, section_or_subsection: Union[Section, Subsection], xml_images: List[XMLImage]) -> None:
        """
        根据XML图片信息更新assets中的characterAssets和propAssets，并上传图片到S3
        
        Args:
            section_or_subsection: 场景或子镜头数据
            xml_images: XML图片信息列表
        """
        # 确保assets字段存在
        if "assets" not in section_or_subsection:
            section_or_subsection["assets"] = {}
        
        # 确保characterAssets和propAssets字段存在
        if "characterAssets" not in section_or_subsection["assets"]:
            section_or_subsection["assets"]["characterAssets"] = []
        if "propAssets" not in section_or_subsection["assets"]:
            section_or_subsection["assets"]["propAssets"] = []
        
        # 遍历XML图片信息，根据type添加到对应的assets中
        for image in xml_images:
            # 准备资产数据
            name = image.get("name", "")
            image_type = image.get("image_type", "jpg")
            
            # 将名称转换为安全的文件名（去除空格和特殊字符）
            safe_name = name.replace(" ", "_").replace("/", "_").replace("\\", "_").lower()
            
            asset = {
                "name": name,
                "description": image.get("description", ""),
                "aspectRatio": image.get("aspect_ratio", "16:9"),
                "image_type": image_type,
                "path": f"{safe_name}.{image_type.lower()}",
                "s3_url": image.get("s3_url", "")
            }
            
            # 确定本地图片文件路径（根据图片类型）
            local_file_path = os.path.join(self.assets_dir, f"image.{image_type}")
            
            # 上传图片到S3
            try:
                # 构建S3路径，与process_asset_image.py保持一致
                s3_file_path = f"svg_video/{self.project_hash}/{safe_name}.{image_type.lower()}"
                self.s3_utils.put_object(s3_file_path, local_file_path)
                print(f"已上传图片到S3: {s3_file_path}")
            except Exception as e:
                print(f"上传图片到S3失败: {str(e)}")
            
            # 根据type添加到对应的assets中
            if image.get("type", "").lower() == "character":
                # 检查是否已存在相同名称的资产
                existing_asset = next((a for a in section_or_subsection["assets"]["characterAssets"] if a["name"] == asset["name"]), None)
                if not existing_asset:
                    section_or_subsection["assets"]["characterAssets"].append(asset)
            elif image.get("type", "").lower() == "prop":
                # 检查是否已存在相同名称的资产
                existing_asset = next((a for a in section_or_subsection["assets"]["propAssets"] if a["name"] == asset["name"]), None)
                if not existing_asset:
                    section_or_subsection["assets"]["propAssets"].append(asset) 