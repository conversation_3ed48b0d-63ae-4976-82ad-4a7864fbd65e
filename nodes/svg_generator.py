import os
import uuid
import hashlib
import json
from typing import Dict, Any, List, Tuple
from langchain_core.messages import BaseMessage

from utils.file_utils import FileManager
from utils.prompt_utils import PromptManager
from utils.s3_utils import SVGSaveS3
from utils.db_utils import write_project_video_scene, write_project_video_scene_material
from utils.svg_utils import SVGUtils
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor
from utils.db_pool import DBConnectionPool

class SVGGenerator:
    """SVG生成节点"""

    def __init__(self, conf_path="../conf/conf.ini"):
        """
        初始化SVG生成节点

        Args:
            conf_path: 配置文件路径
        """
        self.file_manager = FileManager()
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)
        self.conf_path = conf_path

        # 初始化S3存储工具
        self.s3_handler = SVGSaveS3(conf_path)

        # 初始化数据库连接池
        self.db_pool = DBConnectionPool(conf_path)

        # 创建SVG输出目录
        self.svg_output_dir = os.path.join("data", "svg_output")
        os.makedirs(self.svg_output_dir, exist_ok=True)

        # 生成唯一的项目ID
        self.project_id = None

    # def save_scene_assets_to_db(self, project_id: int, scene_order: int, section: Dict[str, Any]) -> None:
    #     """
    #     将场景中的资产保存到数据库

    #     Args:
    #         project_id: 项目ID
    #         scene_order: 场景顺序
    #         section: 场景数据
    #     """
    #     # 获取场景资产
    #     assets = section.get("assets", {})

    #     # 处理人物资产
    #     character_assets = assets.get("characterAssets", [])
    #     for asset in character_assets:
    #         self._save_asset_to_db(project_id, scene_order, asset)

    #     # 处理道具资产
    #     prop_assets = assets.get("propAssets", [])
    #     for asset in prop_assets:
    #         self._save_asset_to_db(project_id, scene_order, asset)

    # def _save_asset_to_db(self, project_id: int, scene_order: int, asset: Dict[str, Any]) -> None:
    #     """
    #     将单个资产保存到数据库

    #     Args:
    #         project_id: 项目ID
    #         scene_order: 场景顺序
    #         asset: 资产数据
    #     """
    #     try:
    #         # 获取资产信息
    #         name = asset.get("name", "未命名资产")
    #         material_path = asset.get("path", "")
    #         material_type = asset.get("image_type", "").lower()

    #         # 如果路径为空或不是S3路径，则跳过
    #         if not material_path:
    #             print(f"资产 {name} 的路径无效或不是S3路径: {material_path}")
    #             return

    #         # 配置JSON，包含资产的描述和宽高比
    #         config = {
    #             "description": asset.get("description", ""),
    #             "aspectRatio": asset.get("aspectRatio", "1:1")
    #         }
    #         config_json = str(config)

    #         # 将资产信息添加到数据库
    #         material_id = write_project_video_scene_material(
    #             self.conf_path,
    #             project_id,
    #             scene_order,
    #             material_type,
    #             material_path,
    #             name,
    #             config_json
    #         )
    #         print(f"已保存资产 {name} 的信息到数据库，材料ID: {material_id}")
    #     except Exception as e:
    #         print(f"保存资产 {name} 到数据库失败: {e}")

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】SVG生成 (SVGGenerator)")
        print("="*50)

        try:
            # 初始化项目信息和目录
            project_dir, project_hash = self._init_project_info(state)
            
            # 执行批处理生成SVG
            results = await self._generate_svg_batch(state, project_hash, project_dir)
            
            # 保存脚本文件
            data_state_path = self._save_script_file(state, project_dir, project_hash)
            
            # 更新状态和保存结果 - 注意这里不需要await，因为_update_state_with_results不是异步函数
            scene_paths = self._update_state_with_results(state, results)
            
            # 添加项目路径到状态中，供后续节点使用
            state["data"]["project_dir"] = project_dir
            state["data"]["data_state_path"] = data_state_path
            state["data"]["scene_paths"] = scene_paths
            
            state["current_step"] = "generate_svg"

            print("="*50)
            print("【完成执行】SVG生成 (SVGGenerator)")
            print("="*50 + "\n")

            return state
        finally:
            # 这里不需要显式关闭连接池，因为连接会自动返回到池中
            pass
            
    def _init_project_info(self, state: Dict[str, Any]) -> Tuple[str, str]:
        """
        初始化项目信息和目录
        
        Args:
            state: 当前状态
            
        Returns:
            Tuple[str, str]: 项目目录路径和项目哈希
        """
        # 获取热词和标题信息
        trend_word = state["data"].get("trend_word", "未知热词")
        title = state["data"].get("news_report", {}).get("title", "未知标题")
        print(f"热词: {trend_word}, 标题: {title}")

        # 生成唯一的项目标识符
        project_hash = hashlib.md5(f"{trend_word}".encode()).hexdigest()
        project_dir = f"svg_video/{project_hash}"
        print(f"项目目录: {project_dir}")

        # 创建项目目录
        os.makedirs(os.path.join(self.svg_output_dir, project_hash), exist_ok=True)
        
        return project_dir, project_hash
    
    async def _generate_svg_batch(self, state: Dict[str, Any], project_hash: str, project_dir: str) -> List[Tuple[str, str, str]]:
        """
        执行批处理生成SVG
        
        Args:
            state: 当前状态
            project_hash: 项目哈希
            project_dir: 项目目录
            
        Returns:
            List[Tuple[str, str, str]]: 生成结果列表，每项包含(场景ID, SVG代码, S3路径)
        """
        # 从news_report中获取SVG提示词
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])
        
        # 准备批处理数据
        batch_items = []

        # 收集所有需要处理的场景和镜头
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"
            
            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])
            
            if subsections:
                # 如果有子镜头，则处理每个子镜头的svg_prompt
                for j, subsection in enumerate(subsections):
                    shot_id = f"{section_id}_shot_{j+1}"
                    svg_prompt = subsection.get("svg_prompt")
                    if svg_prompt:
                        batch_items.append((shot_id, svg_prompt))
                        print(f"准备处理场景 {section_id} 的镜头 {j+1} 的SVG生成")
            else:
                # 如果没有子镜头，则处理整个场景的svg_prompt
                svg_prompt = section.get("svg_prompt")
                if svg_prompt:
                    batch_items.append((section_id, svg_prompt))
                    print(f"准备处理场景 {i+1} 的SVG生成")

        print(f"准备批处理 {len(batch_items)} 个场景的SVG生成")
        
        # 定义一个异步的包装函数来处理响应
        async def process_svg_response_wrapper(item, response):
            return await self._process_svg_response(item, response, project_hash, project_dir)
        
        # 执行批处理（启用深度思考，使用temperature=0.0以获得确定性输出）
        results = await self.batch_processor.process_batch(
            items=batch_items,
            create_messages_func=self._create_messages_for_svg,
            process_response_func=process_svg_response_wrapper,
            max_retries=5,
            temperature=0.0
        )
        
        return results
    
    def _create_messages_for_svg(self, item: Tuple[str, str]) -> List[BaseMessage]:
        """
        创建SVG生成的消息
        
        Args:
            item: 包含场景ID和提示词的元组
            
        Returns:
            List[BaseMessage]: 消息列表
        """
        scene_id, svg_prompt = item
        print(f"准备处理场景: {scene_id}")
        return self.llm.create_messages(
            system_prompt="""
                # 角色：SVG动画代码编写专家 (SVG Animation Code Expert)
                
                ## 核心身份
                你是一位精通SVG技术的高级动画开发工程师，拥有丰富的SVG SMIL动画、CSS动画和复杂时间线控制经验。你的专长是将详细的动画设计文档精确转换为可执行的SVG代码。
                
                ## 核心任务
                接收用户提供的**SVG动画设计文档**，严格按照文档中的每一个细节要求，生成完整的、可直接运行的SVG动画代码。你必须确保生成的代码100%忠实于设计文档的所有规格说明。
                
                ## 关键要求
                
                ### 1. 完全遵循设计文档
                - **严格按照文档结构**：逐一实现文档中列出的每个元素、每个动画轨道、每个时间点
                - **精确坐标定位**：使用文档中指定的确切坐标(x,y)、尺寸、缩放比例
                - **严格时间控制**：动画的开始时间、持续时间、结束时间必须与文档完全一致
                - **忠实视觉效果**：颜色、字体、透明度、缓动函数等视觉属性必须按文档执行
                
                ### 2. 技术实现规范
                - **优先使用SVG SMIL**：使用`<animate>`、`<animateTransform>`、`<animateMotion>`等SMIL元素
                - **CSS动画作为补充**：对于SMIL难以实现的复杂效果，使用内联CSS @keyframes
                - **避免外部依赖**：不使用外部JavaScript库，确保SVG文件独立运行
                - **图片资源引用**：使用`<image xlink:href="资产路径">`方式引用外部图片
                
                ### 3. 代码质量标准
                - **结构清晰**：代码层次分明，元素分组合理，注释详细
                - **性能优化**：避免冗余动画，合理使用图层顺序(z-index)
                - **兼容性良好**：确保主流浏览器支持
                - **易于维护**：变量命名规范，动画逻辑清晰
                
                ## 输出规范
                
                ### 代码结构要求
                ```xml
                <?xml version="1.0" encoding="UTF-8"?>
                <svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <!-- 样式定义区域 -->
                  <defs>
                    <style>
                      /* CSS动画定义 */
                    </style>
                  </defs>
                  
                  <!-- 背景层 -->
                  <!-- 图片元素层 -->
                  <!-- 文字元素层 -->
                  <!-- 动画控制层 -->
                </svg>
                ```
                
                ### 注释规范
                - 每个主要元素组都要有清晰的注释说明
                - 复杂动画序列要标注时间节点
                - 关键坐标和尺寸要添加说明注释
                
                ### 动画实现要点
                1. **时间轴精确控制**：使用`begin`、`dur`、`end`属性精确控制动画时机
                2. **缓动函数映射**：将文档中的缓动函数(ease-in、ease-out等)正确转换为SVG支持的格式
                3. **多属性同步**：确保同一元素的位置、透明度、缩放等多属性动画同步进行
                4. **图层管理**：通过SVG分组和CSS z-index正确处理元素层次关系
                
                ## 特殊处理指南
                
                ### 文字动画处理
                - **避免透明度动画**：按文档要求，文字元素不使用opacity动画
                - **打字机效果**：使用`<animate>`逐步显示文字内容
                - **文字路径动画**：复杂文字路径使用`<textPath>`和`<animateMotion>`
                
                ### 图片元素处理
                - **资产路径保持**：严格使用文档提供的资产路径，不做任何修改
                - **尺寸比例维护**：确保图片在缩放过程中保持原始宽高比
                - **位置变换精确**：图片移动轨迹必须符合文档描述的路径
                
                ### 复杂动画序列
                - **关键帧细分**：将复杂动画分解为多个关键帧，确保中间状态准确
                - **动画链式控制**：使用`begin="前一动画.end"`实现动画序列连接
                - **状态保持**：动画结束后使用`fill="freeze"`保持最终状态
                
                ## 工作流程
                
                1. **文档解析阶段**
                   - 仔细阅读整个设计文档
                   - 提取所有关键信息：元素列表、时间线、坐标、尺寸等
                   - 识别技术难点和特殊要求
                
                2. **代码架构设计**
                   - 根据元素类型和图层关系设计SVG结构
                   - 规划动画实现方式（SMIL vs CSS）
                   - 确定代码组织方式和命名规范
                
                3. **逐步实现**
                   - 先实现静态元素布局
                   - 再添加动画效果
                   - 最后优化性能和兼容性
                
                4. **质量检查**
                   - 对照设计文档逐项验证
                   - 检查时间线是否准确
                   - 确认视觉效果是否符合预期
                
                ## 输出要求
                
                - **直接输出完整SVG代码**，无需额外解释
                - **代码必须可以直接保存为.svg文件并在浏览器中运行**
                - **所有动画效果必须在代码中完整实现**
                - **严格遵循设计文档的每一个细节要求**
                
                ---
                
                **请提供你的SVG动画设计文档，我将为你生成完全符合要求的SVG动画代码。**
            """,
            history=[],
            query=svg_prompt
        )
    
    async def _process_svg_response(self, item: Tuple[str, str], response: str, project_hash: str, project_dir: str) -> Tuple[str, str, str]:
        """
        处理SVG生成响应
        
        Args:
            item: 包含场景ID和提示词的元组
            response: LLM生成的响应
            project_hash: 项目哈希
            project_dir: 项目目录
            
        Returns:
            Tuple[str, str, str]: 包含(场景ID, SVG代码, S3路径)的元组
        """
        scene_id, scene_prompt = item
        max_retries = 5
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 提取SVG代码
                svg_code = SVGUtils.extract_svg_code(response)

                # 检查是否成功提取到SVG代码
                if not svg_code or '<svg' not in svg_code or '</svg>' not in svg_code:
                    print(f"第{retry_count + 1}次尝试：未能提取有效的SVG代码，将重新生成")
                    raise ValueError("SVG提取失败，需要重新生成")

                # 验证SVG代码的有效性
                is_valid, error_msg = SVGUtils.validate_svg(svg_code)
                if not is_valid:
                    print(f"第{retry_count + 1}次尝试：SVG代码验证失败: {error_msg}")
                    raise ValueError(f"SVG验证失败: {error_msg}")

                print(f"成功提取并验证SVG代码")

                # 保存SVG文件到本地并上传到S3
                s3_path = self._save_svg_file(scene_id, svg_code, project_hash, project_dir)
                
                return scene_id, svg_code, s3_path

            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"达到最大重试次数({max_retries})，生成失败")
                    raise e
                print(f"第{retry_count}次尝试失败，准备第{retry_count + 1}次重试")
                # 重新生成SVG代码 - 使用异步方法
                messages = self.llm.create_messages(
                    system_prompt="你是一个专门从事SVG动画的AI助手。对用户提供的动画设计文档，生成相应的、完整的、有效的SVG代码。",
                    history=[],
                    query=scene_prompt
                )
                # 调用异步API生成响应，设置temperature为0
                result = await self.llm.generate_response(messages, temperature=0.0)
                response = result
    
    def _save_svg_file(self, scene_id: str, svg_code: str, project_hash: str, project_dir: str) -> str:
        """
        保存SVG文件到本地并上传到S3
        
        Args:
            scene_id: 场景ID
            svg_code: SVG代码
            project_hash: 项目哈希
            project_dir: 项目目录
            
        Returns:
            str: S3路径
        """
        # 保存SVG文件到本地
        svg_filename = f"scene_{scene_id}.svg"
        local_svg_path = os.path.join(self.svg_output_dir, project_hash, svg_filename)
        with open(local_svg_path, "w", encoding="utf-8") as f:
            f.write(svg_code)
        print(f"已保存场景 {scene_id} 的SVG文件到本地: {local_svg_path}")

        # 构建S3路径
        s3_svg_path = f"{project_dir}/{svg_filename}"

        # 上传SVG文件到S3
        try:
            self.s3_handler.put_object_by_content(s3_svg_path, svg_code)
            print(f"已上传场景 {scene_id} 的SVG文件到S3: {s3_svg_path}")
        except Exception as e:
            print(f"上传SVG文件到S3失败: {e}")
            
        return s3_svg_path
    
    def _save_script_file(self, state: Dict[str, Any], project_dir: str, project_hash: str) -> str:
        """
        保存脚本文件到本地和S3
        
        Args:
            state: 当前状态
            project_dir: 项目目录
            project_hash: 项目哈希
            
        Returns:
            str: 数据状态路径
        """
        # 保存脚本文件
        data_state_content = state["data"]
        data_state_path = f"{project_dir}/data_state.json"
        local_data_state_path = os.path.join(self.svg_output_dir, project_hash, "data_state.json")

        # 保存脚本到本地
        # 创建目录路径（如果不存在）
        os.makedirs(os.path.dirname(local_data_state_path), exist_ok=True)
        
        # 直接使用open写入，避免使用file_manager.write_json
        with open(local_data_state_path, "w", encoding="utf-8") as f:
            json.dump(data_state_content, f, ensure_ascii=False, indent=2)

        # 上传脚本到S3
        try:
            self.s3_handler.put_object_by_content(data_state_path, json.dumps(data_state_content, ensure_ascii=False, indent=2), mimetype="application/json")
            print(f"已上传脚本文件到S3: {data_state_path}")
        except Exception as e:
            print(f"上传脚本文件到S3失败: {e}")
            
        return data_state_path
    
    def _update_state_with_results(self, state: Dict[str, Any], results: List[Tuple[str, str, str]]) -> List[Tuple[str, str]]:
        """
        更新状态中的SVG文件信息
        
        Args:
            state: 当前状态
            results: 生成结果列表
            
        Returns:
            List[Tuple[str, str]]: 场景路径列表，每项包含(场景ID, S3路径)
        """
        # 更新状态
        state["data"]["svg_files"] = state["data"].get("svg_files", {})
        state["data"]["svg_paths"] = state["data"].get("svg_paths", {})
        
        # 将结果添加到状态中
        scene_paths = []
        for scene_id, svg_code, s3_path in results:
            state["data"]["svg_files"][scene_id] = svg_code
            state["data"]["svg_paths"][scene_id] = s3_path
            scene_paths.append((scene_id, s3_path))
            
            # 将SVG代码和路径添加到news_report的对应分镜中
            self._update_news_report_with_svg(state, scene_id, svg_code, s3_path)
            
        return scene_paths
    
    def _update_news_report_with_svg(self, state: Dict[str, Any], scene_id: str, svg_code: str, s3_path: str) -> None:
        """
        将SVG代码和路径添加到news_report的对应分镜中
        
        Args:
            state: 当前状态
            scene_id: 场景ID
            svg_code: SVG代码
            s3_path: S3路径
        """
        # 获取news_report
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])
        
        # 解析场景ID
        if "_shot_" in scene_id:
            # 镜头格式: section_X_shot_Y
            parts = scene_id.split('_')
            section_index = int(parts[1]) - 1
            shot_index = int(parts[3]) - 1
            
            # 确保索引有效
            if 0 <= section_index < len(sections) and "subsections" in sections[section_index]:
                subsections = sections[section_index].get("subsections", [])
                if 0 <= shot_index < len(subsections):
                    # 更新子镜头的svg_code和svg_path
                    sections[section_index]["subsections"][shot_index]["svg_code"] = svg_code
                    sections[section_index]["subsections"][shot_index]["svg_path"] = s3_path
                    print(f"已更新 {scene_id} 的svg_code和svg_path")
        else:
            # 普通场景格式: section_X
            section_index = int(scene_id.split('_')[1]) - 1
            if 0 <= section_index < len(sections):
                # 更新场景的svg_code和svg_path
                sections[section_index]["svg_code"] = svg_code
                sections[section_index]["svg_path"] = s3_path
                print(f"已更新 {scene_id} 的svg_code和svg_path")
        
        # 更新news_report
        state["data"]["news_report"] = news_report