import os
import uuid
import hashlib
import json
from typing import Dict, Any, List, Tuple
from langchain_core.messages import BaseMessage

from utils.file_utils import FileManager
from utils.prompt_utils import PromptManager
from utils.s3_utils import SVGSaveS3
from utils.db_utils import write_project_video_scene, write_project_video_scene_material
from utils.manim_utils import ManimUtils
from llm.claude_local import ClaudeLocalProvider
from llm.batch_processor import BatchProcessor
from utils.db_pool import DBConnectionPool

class ManimGenerator:
    """Manim代码生成节点"""

    def __init__(self, conf_path="../conf/conf.ini"):
        """
        初始化Manim代码生成节点

        Args:
            conf_path: 配置文件路径
        """
        self.file_manager = FileManager()
        self.prompt_manager = PromptManager()
        self.llm = ClaudeLocalProvider()
        self.batch_processor = BatchProcessor(provider=self.llm, max_concurrent=10)
        self.conf_path = conf_path

        # 初始化S3存储工具
        self.s3_handler = SVGSaveS3(conf_path)

        # 初始化数据库连接池
        self.db_pool = DBConnectionPool(conf_path)

        # 创建Manim输出目录
        self.manim_output_dir = os.path.join("data", "manim_output")
        os.makedirs(self.manim_output_dir, exist_ok=True)

        # 生成唯一的项目ID
        self.project_id = None

    async def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理数据

        Args:
            state: 当前状态

        Returns:
            Dict[str, Any]: 更新后的状态
        """
        print("\n" + "="*50)
        print("【开始执行】Manim代码生成 (ManimGenerator)")
        print("="*50)

        try:
            # 初始化项目信息和目录
            project_dir, project_hash = self._init_project_info(state)

            # 执行批处理生成Manim代码
            results = await self._generate_manim_batch(state, project_hash, project_dir)

            # 保存脚本文件
            data_state_path = self._save_script_file(state, project_dir, project_hash)

            # 更新状态和保存结果
            scene_paths = self._update_state_with_results(state, results)

            # 添加项目路径到状态中，供后续节点使用
            state["data"]["project_dir"] = project_dir
            state["data"]["data_state_path"] = data_state_path
            state["data"]["scene_paths"] = scene_paths

            state["current_step"] = "generate_manim"

            print("="*50)
            print("【完成执行】Manim代码生成 (ManimGenerator)")
            print("="*50 + "\n")

            return state
        finally:
            # 这里不需要显式关闭连接池，因为连接会自动返回到池中
            pass

    def _init_project_info(self, state: Dict[str, Any]) -> Tuple[str, str]:
        """
        初始化项目信息和目录

        Args:
            state: 当前状态

        Returns:
            Tuple[str, str]: 项目目录路径和项目哈希
        """
        # 获取热词和标题信息
        trend_word = state["data"].get("trend_word", "未知热词")
        title = state["data"].get("news_report", {}).get("title", "未知标题")
        print(f"热词: {trend_word}, 标题: {title}")

        # 生成唯一的项目标识符
        project_hash = hashlib.md5(f"{trend_word}".encode()).hexdigest()
        project_dir = f"manim_video/{project_hash}"
        print(f"项目目录: {project_dir}")

        # 创建项目目录
        os.makedirs(os.path.join(self.manim_output_dir, project_hash), exist_ok=True)

        return project_dir, project_hash

    async def _generate_manim_batch(self, state: Dict[str, Any], project_hash: str, project_dir: str) -> List[Tuple[str, str, str]]:
        """
        执行批处理生成Manim代码

        Args:
            state: 当前状态
            project_hash: 项目哈希
            project_dir: 项目目录

        Returns:
            List[Tuple[str, str, str]]: 生成结果列表，每项包含(场景ID, Manim代码, S3路径)
        """
        # 从news_report中获取SVG提示词（现在用于Manim代码生成）
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])

        # 准备批处理数据
        batch_items = []

        # 收集所有需要处理的场景和镜头
        for i, section in enumerate(sections):
            section_id = f"section_{i+1}"

            # 获取当前场景的子镜头列表
            subsections = section.get("subsections", [])

            if subsections:
                # 如果有子镜头，则处理每个子镜头的svg_prompt
                for j, subsection in enumerate(subsections):
                    shot_id = f"{section_id}_shot_{j+1}"
                    svg_prompt = subsection.get("svg_prompt")
                    if svg_prompt:
                        batch_items.append((shot_id, svg_prompt))
                        print(f"准备处理场景 {section_id} 的镜头 {j+1} 的Manim代码生成")
            else:
                # 如果没有子镜头，则处理整个场景的svg_prompt
                svg_prompt = section.get("svg_prompt")
                if svg_prompt:
                    batch_items.append((section_id, svg_prompt))
                    print(f"准备处理场景 {i+1} 的Manim代码生成")

        print(f"准备批处理 {len(batch_items)} 个场景的Manim代码生成")

        # 定义一个异步的包装函数来处理响应
        async def process_manim_response_wrapper(item, response):
            return await self._process_manim_response(item, response, project_hash, project_dir)

        # 执行批处理（启用深度思考，使用temperature=0.0以获得确定性输出）
        results = await self.batch_processor.process_batch(
            items=batch_items,
            create_messages_func=self._create_messages_for_manim,
            process_response_func=process_manim_response_wrapper,
            max_retries=5,
            temperature=0.0
        )

        return results

    def _create_messages_for_manim(self, item: Tuple[str, str]) -> List[BaseMessage]:
        """
        创建Manim代码生成的消息

        Args:
            item: 包含场景ID和提示词的元组

        Returns:
            List[BaseMessage]: 消息列表
        """
        scene_id, animation_prompt = item
        print(f"准备处理场景: {scene_id}")
        return self.llm.create_messages(
            system_prompt="""
                # 角色：Manim动画代码编写专家 (Manim Animation Code Expert)

                ## 核心身份
                你是一位精通Manim技术的高级动画开发工程师，拥有丰富的Manim动画、Python编程和复杂时间线控制经验。你的专长是将详细的动画设计文档精确转换为可执行的Manim Python代码。

                ## 核心任务
                接收用户提供的**动画设计文档**，严格按照文档中的每一个细节要求，生成完整的、可直接运行的Manim动画Python代码。你必须确保生成的代码100%忠实于设计文档的所有规格说明。

                ## 关键要求

                ### 1. 完全遵循设计文档
                - **严格按照文档结构**：逐一实现文档中列出的每个元素、每个动画轨道、每个时间点
                - **精确坐标定位**：使用文档中指定的确切坐标(x,y)、尺寸、缩放比例
                - **严格时间控制**：动画的开始时间、持续时间、结束时间必须与文档完全一致
                - **忠实视觉效果**：颜色、字体、透明度、缓动函数等视觉属性必须按文档执行

                ### 2. 技术实现规范
                - **使用Manim库**：使用`from manim import *`导入所有必要的类和函数
                - **Scene类继承**：创建继承自Scene的类来实现动画
                - **construct方法**：在construct方法中实现所有动画逻辑
                - **图片资源引用**：使用`ImageMobject("资产路径")`方式引用外部图片

                ### 3. 代码质量标准
                - **结构清晰**：代码层次分明，元素分组合理，注释详细
                - **性能优化**：避免冗余动画，合理使用图层顺序
                - **易于维护**：变量命名规范，动画逻辑清晰

                ## 输出规范

                ### 代码结构要求
                ```python
                from manim import *
                import os

                class AnimationScene(Scene):
                    def construct(self):
                        """
                        动画场景描述
                        总时长：X秒，严格按照动画设计文档实现
                        画布尺寸：1920×1080像素 (16:9)
                        """
                        # 设置画布背景
                        self.camera.background_color = WHITE

                        # 图片资源路径定义
                        # 严格按照设计文档中的资产路径

                        # 检查文件是否存在

                        # 创建图片对象

                        # 创建文字对象

                        # 动画时间线实现
                ```

                ### 注释规范
                - 每个主要元素组都要有清晰的注释说明
                - 复杂动画序列要标注时间节点
                - 关键坐标和尺寸要添加说明注释

                ### 动画实现要点
                1. **时间轴精确控制**：使用`self.play()`的`run_time`参数精确控制动画时机
                2. **缓动函数映射**：将文档中的缓动函数(ease-in、ease-out等)正确转换为Manim支持的格式
                3. **多属性同步**：确保同一元素的位置、透明度、缩放等多属性动画同步进行
                4. **图层管理**：通过添加顺序和z_index正确处理元素层次关系

                ## 特殊处理指南

                ### 文字动画处理
                - **使用Text对象**：使用`Text()`创建文字对象
                - **打字机效果**：使用`Write()`动画实现打字机效果
                - **文字属性**：正确设置字体、大小、颜色等属性

                ### 图片元素处理
                - **资产路径保持**：严格使用文档提供的资产路径，不做任何修改
                - **尺寸比例维护**：确保图片在缩放过程中保持原始宽高比
                - **位置变换精确**：图片移动轨迹必须符合文档描述的路径

                ### 复杂动画序列
                - **关键帧细分**：将复杂动画分解为多个self.play()调用
                - **动画链式控制**：使用连续的self.play()调用实现动画序列
                - **状态保持**：动画结束后元素保持最终状态

                ## 工作流程

                1. **文档解析阶段**
                   - 仔细阅读整个设计文档
                   - 提取所有关键信息：元素列表、时间线、坐标、尺寸等
                   - 识别技术难点和特殊要求

                2. **代码架构设计**
                   - 根据元素类型和图层关系设计代码结构
                   - 规划动画实现方式
                   - 确定代码组织方式和命名规范

                3. **逐步实现**
                   - 先创建静态元素
                   - 再添加动画效果
                   - 最后优化性能和时间线

                4. **质量检查**
                   - 对照设计文档逐项验证
                   - 检查时间线是否准确
                   - 确认视觉效果是否符合预期

                ## 输出要求

                - **直接输出完整Python代码**，无需额外解释
                - **代码必须可以直接保存为.py文件并运行**
                - **所有动画效果必须在代码中完整实现**
                - **严格遵循设计文档的每一个细节要求**

                ---

                **请提供你的动画设计文档，我将为你生成完全符合要求的Manim动画代码。**
            """,
            history=[],
            query=animation_prompt
        )

    async def _process_manim_response(self, item: Tuple[str, str], response: str, project_hash: str, project_dir: str) -> Tuple[str, str, str]:
        """
        处理Manim代码生成响应

        Args:
            item: 包含场景ID和提示词的元组
            response: LLM生成的响应
            project_hash: 项目哈希
            project_dir: 项目目录

        Returns:
            Tuple[str, str, str]: 包含(场景ID, Manim代码, S3路径)的元组
        """
        scene_id, scene_prompt = item
        max_retries = 5
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 提取Manim代码
                manim_code = ManimUtils.extract_manim_code(response)

                # 检查是否成功提取到代码内容
                if not manim_code:
                    print(f"第{retry_count + 1}次尝试：未能提取到代码内容，将重新生成")
                    raise ValueError("代码提取失败，需要重新生成")

                print(f"成功提取Manim代码")

                # 保存Manim文件到本地并上传到S3
                s3_path = self._save_manim_file(scene_id, manim_code, project_hash, project_dir)

                return scene_id, manim_code, s3_path

            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"达到最大重试次数({max_retries})，生成失败")
                    raise e
                print(f"第{retry_count}次尝试失败，准备第{retry_count + 1}次重试")
                # 重新生成Manim代码 - 使用异步方法
                messages = self.llm.create_messages(
                    system_prompt="你是一个专门从事Manim动画的AI助手。对用户提供的动画设计文档，生成相应的、完整的、有效的Manim Python代码。",
                    history=[],
                    query=scene_prompt
                )
                # 调用异步API生成响应，设置temperature为0
                result = await self.llm.generate_response(messages, temperature=0.0)
                response = result

    def _save_manim_file(self, scene_id: str, manim_code: str, project_hash: str, project_dir: str) -> str:
        """
        保存Manim文件到本地并上传到S3

        Args:
            scene_id: 场景ID
            manim_code: Manim代码
            project_hash: 项目哈希
            project_dir: 项目目录

        Returns:
            str: S3路径
        """
        # 保存Manim文件到本地
        manim_filename = f"scene_{scene_id}.py"
        local_manim_path = os.path.join(self.manim_output_dir, project_hash, manim_filename)
        with open(local_manim_path, "w", encoding="utf-8") as f:
            f.write(manim_code)
        print(f"已保存场景 {scene_id} 的Manim文件到本地: {local_manim_path}")

        # 构建S3路径
        s3_manim_path = f"{project_dir}/{manim_filename}"

        # 上传Manim文件到S3
        try:
            self.s3_handler.put_object_by_content(s3_manim_path, manim_code)
            print(f"已上传场景 {scene_id} 的Manim文件到S3: {s3_manim_path}")
        except Exception as e:
            print(f"上传Manim文件到S3失败: {e}")

        return s3_manim_path

    def _save_script_file(self, state: Dict[str, Any], project_dir: str, project_hash: str) -> str:
        """
        保存脚本文件到本地和S3

        Args:
            state: 当前状态
            project_dir: 项目目录
            project_hash: 项目哈希

        Returns:
            str: 数据状态路径
        """
        # 保存脚本文件
        data_state_content = state["data"]
        data_state_path = f"{project_dir}/data_state.json"
        local_data_state_path = os.path.join(self.manim_output_dir, project_hash, "data_state.json")

        # 保存脚本到本地
        # 创建目录路径（如果不存在）
        os.makedirs(os.path.dirname(local_data_state_path), exist_ok=True)

        # 直接使用open写入，避免使用file_manager.write_json
        with open(local_data_state_path, "w", encoding="utf-8") as f:
            json.dump(data_state_content, f, ensure_ascii=False, indent=2)

        # 上传脚本到S3
        try:
            self.s3_handler.put_object_by_content(data_state_path, json.dumps(data_state_content, ensure_ascii=False, indent=2), mimetype="application/json")
            print(f"已上传脚本文件到S3: {data_state_path}")
        except Exception as e:
            print(f"上传脚本文件到S3失败: {e}")

        return data_state_path

    def _update_state_with_results(self, state: Dict[str, Any], results: List[Tuple[str, str, str]]) -> List[Tuple[str, str]]:
        """
        更新状态中的Manim文件信息

        Args:
            state: 当前状态
            results: 生成结果列表

        Returns:
            List[Tuple[str, str]]: 场景路径列表，每项包含(场景ID, S3路径)
        """
        # 更新状态
        state["data"]["manim_files"] = state["data"].get("manim_files", {})
        state["data"]["manim_paths"] = state["data"].get("manim_paths", {})

        # 将结果添加到状态中
        scene_paths = []
        for scene_id, manim_code, s3_path in results:
            state["data"]["manim_files"][scene_id] = manim_code
            state["data"]["manim_paths"][scene_id] = s3_path
            scene_paths.append((scene_id, s3_path))

            # 将Manim代码和路径添加到news_report的对应分镜中
            self._update_news_report_with_manim(state, scene_id, manim_code, s3_path)

        return scene_paths

    def _update_news_report_with_manim(self, state: Dict[str, Any], scene_id: str, manim_code: str, s3_path: str) -> None:
        """
        将Manim代码和路径添加到news_report的对应分镜中

        Args:
            state: 当前状态
            scene_id: 场景ID
            manim_code: Manim代码
            s3_path: S3路径
        """
        # 获取news_report
        news_report = state["data"].get("news_report", {})
        sections = news_report.get("sections", [])

        # 解析场景ID
        if "_shot_" in scene_id:
            # 镜头格式: section_X_shot_Y
            parts = scene_id.split('_')
            section_index = int(parts[1]) - 1
            shot_index = int(parts[3]) - 1

            # 确保索引有效
            if 0 <= section_index < len(sections) and "subsections" in sections[section_index]:
                subsections = sections[section_index].get("subsections", [])
                if 0 <= shot_index < len(subsections):
                    # 更新子镜头的manim_code和manim_path
                    sections[section_index]["subsections"][shot_index]["manim_code"] = manim_code
                    sections[section_index]["subsections"][shot_index]["manim_path"] = s3_path
                    print(f"已更新 {scene_id} 的manim_code和manim_path")
        else:
            # 普通场景格式: section_X
            section_index = int(scene_id.split('_')[1]) - 1
            if 0 <= section_index < len(sections):
                # 更新场景的manim_code和manim_path
                sections[section_index]["manim_code"] = manim_code
                sections[section_index]["manim_path"] = s3_path
                print(f"已更新 {scene_id} 的manim_code和manim_path")

        # 更新news_report
        state["data"]["news_report"] = news_report