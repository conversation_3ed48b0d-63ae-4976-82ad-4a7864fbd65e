<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#FFFFFF"/>
  
  <!-- 主标题 "耳环风波" -->
  <text id="main-title" x="960" y="54" font-family="Arial, sans-serif" font-size="42" fill="#000000" text-anchor="middle" dominant-baseline="middle" opacity="0">
    耳环风波
    <animateTransform
      attributeName="transform"
      type="translate"
      values="0,-104; 0,0"
      dur="1s"
      begin="0s"
      calcMode="spline"
      keySplines="0.25 0.1 0.25 1"
      fill="freeze"/>
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="1s"
      begin="0s"
      fill="freeze"/>
  </text>
  
  <!-- 日期标签 "2025年5月" -->
  <text id="date-label" x="100" y="54" font-family="Arial, sans-serif" font-size="24" fill="#888888" text-anchor="start" dominant-baseline="middle" opacity="0">
    2025年5月
    <animate
      attributeName="opacity"
      values="0; 1; 1; 0"
      dur="3s"
      begin="0.5s"
      keyTimes="0; 0.33; 0.67; 1"
      fill="freeze"/>
  </text>
  
  <!-- 黄杨钿甜人物照片 -->
  <image id="person-image" xlink:href="黄杨钿甜.jpg" x="810" y="390" width="300" height="300" opacity="0">
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0.8; 1.0; 0.6"
      dur="6s"
      begin="0s"
      keyTimes="0; 0.5; 1"
      calcMode="spline"
      keySplines="0.25 0.1 0.25 1; 0.42 0 0.58 1"
      fill="freeze"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="0,0; 0,0; -576,-270"
      dur="6s"
      begin="0s"
      keyTimes="0; 0.5; 1"
      calcMode="spline"
      keySplines="0.25 0.1 0.25 1; 0.42 0 0.58 1"
      fill="freeze"
      additive="sum"/>
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="3s"
      begin="0s"
      fill="freeze"/>
  </image>
  
  <!-- 耳环图片 -->
  <image id="earring-image" xlink:href="黄杨钿甜佩戴的graff耳环.jpg" x="1386" y="120" width="300" height="300" opacity="0">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="564,0; 0,0"
      dur="2s"
      begin="3s"
      calcMode="spline"
      keySplines="0.25 0.1 0.25 1"
      fill="freeze"/>
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="0.5s"
      begin="3s"
      fill="freeze"/>
    <!-- 耳环特写效果 -->
    <animateTransform
      attributeName="transform"
      type="scale"
      values="1.0; 1.1; 1.0"
      dur="0.5s"
      begin="5s"
      calcMode="spline"
      keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"
      additive="sum"/>
  </image>
  
  <!-- 耳环标签 -->
  <g id="earring-label" opacity="0">
    <rect x="1436" y="375" width="200" height="30" fill="#000000" opacity="0.7" rx="5"/>
    <text x="1536" y="390" font-family="Arial, sans-serif" font-size="24" fill="#FFFFFF" text-anchor="middle" dominant-baseline="middle">
      GRAFF祖母绿钻石耳环
    </text>
    <animate
      attributeName="opacity"
      values="0; 1; 1; 0"
      dur="10s"
      begin="5s"
      keyTimes="0; 0.1; 0.9; 1"
      fill="freeze"/>
  </g>
  
  <!-- 初始价格 "230万元？" 和叉号 -->
  <g id="initial-price" opacity="0">
    <text x="960" y="650" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#FF0000" text-anchor="middle" dominant-baseline="middle">
      230万元？
    </text>
    <text x="1060" y="650" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#FF0000" text-anchor="middle" dominant-baseline="middle">
      ✖
    </text>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="0,100; 0,0; 0,5; 0,-5; 0,0"
      dur="3s"
      begin="6s"
      keyTimes="0; 0.17; 0.33; 0.5; 0.67"
      calcMode="spline"
      keySplines="0.68 -0.55 0.265 1.55; 0.25 0.46 0.45 0.94; 0.25 0.46 0.45 0.94; 0.25 0.46 0.45 0.94"
      fill="freeze"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0.8; 1.0"
      dur="0.5s"
      begin="6s"
      calcMode="spline"
      keySplines="0.68 -0.55 0.265 1.55"
      additive="sum"
      fill="freeze"/>
    <animate
      attributeName="opacity"
      values="0; 1; 1; 0"
      dur="3s"
      begin="6s"
      keyTimes="0; 0.17; 0.83; 1"
      fill="freeze"/>
  </g>
  
  <!-- 珠宝博主澄清截图 -->
  <image id="blogger-clarification" xlink:href="珠宝博主澄清截图.jpg" x="384" y="606" width="1152" height="300" opacity="0">
    <animateTransform
      attributeName="transform"
      type="translate"
      values="240,144; 0,0"
      dur="0.8s"
      begin="9s"
      calcMode="spline"
      keySplines="0.25 0.1 0.25 1"
      fill="freeze"/>
    <animate
      attributeName="opacity"
      values="0; 1; 1; 0"
      dur="3s"
      begin="9s"
      keyTimes="0; 0.27; 0.83; 1"
      fill="freeze"/>
  </image>
  
  <!-- 博主澄清标签 -->
  <text id="blogger-label" x="1250" y="650" font-family="Arial, sans-serif" font-size="20" font-style="italic" fill="#888888" text-anchor="end" dominant-baseline="middle" opacity="0">
    珠宝博主澄清
    <animate
      attributeName="opacity"
      values="0; 1; 1; 0"
      dur="2.6s"
      begin="9.4s"
      keyTimes="0; 0.19; 0.81; 1"
      fill="freeze"/>
  </text>
  
  <!-- 更正价格 "15-60万元" 和勾号 -->
  <g id="correct-price" opacity="0">
    <text x="960" y="650" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#008000" text-anchor="middle" dominant-baseline="middle">
      15-60万元
    </text>
    <text x="1060" y="650" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#008000" text-anchor="middle" dominant-baseline="middle">
      ✓
    </text>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="0,100; 0,0; 0,-250"
      dur="6s"
      begin="9s"
      keyTimes="0; 0.5; 1"
      calcMode="spline"
      keySplines="0.25 0.1 0.25 1; 0.42 0 0.58 1"
      fill="freeze"/>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="0.8; 1.0"
      dur="0.5s"
      begin="9s"
      calcMode="spline"
      keySplines="0.25 0.1 0.25 1"
      additive="sum"
      fill="freeze"/>
    <animate
      attributeName="opacity"
      values="0; 1; 1; 0"
      dur="6s"
      begin="9s"
      keyTimes="0; 0.08; 0.92; 1"
      fill="freeze"/>
  </g>
  
  <!-- 对话气泡背景 -->
  <g id="dialogue-bubble" opacity="0">
    <!-- 气泡背景 -->
    <path d="M 700 480 Q 700 460 720 460 L 1200 460 Q 1220 460 1220 480 L 1220 580 Q 1220 600 1200 600 L 780 600 L 740 620 L 780 600 L 720 600 Q 700 600 700 580 Z" 
          fill="#F0F0F0" stroke="#CCCCCC" stroke-width="2"/>
    <!-- 小头像 -->
    <circle cx="730" cy="530" r="25" fill="#E0E0E0"/>
    <text x="730" y="535" font-family="Arial, sans-serif" font-size="12" fill="#666666" text-anchor="middle" dominant-baseline="middle">头像</text>
    <animate
      attributeName="opacity"
      values="0; 1; 1; 0"
      dur="3s"
      begin="12s"
      keyTimes="0; 0.07; 0.83; 1"
      fill="freeze"/>
  </g>
  
  <!-- 黄杨钿甜回应标签 -->
  <text id="response-label" x="960" y="520" font-family="Arial, sans-serif" font-size="24" fill="#0066CC" text-anchor="middle" dominant-baseline="middle" opacity="0">
    黄杨钿甜回应:
    <animate
      attributeName="opacity"
      values="0; 1; 1; 0"
      dur="3s"
      begin="12s"
      keyTimes="0; 0.07; 0.83; 1"
      fill="freeze"/>
  </text>
  
  <!-- 打字机效果的回应文字 -->
  <text id="response-text" x="960" y="560" font-family="Arial, sans-serif" font-size="36" fill="#000000" text-anchor="middle" dominant-baseline="middle" opacity="0">
    <animate
      attributeName="opacity"
      values="0; 1; 1; 0"
      dur="3s"
      begin="12.2s"
      keyTimes="0; 0.03; 0.8; 1"
      fill="freeze"/>
    <!-- 打字机效果通过多个tspan实现 -->
    <tspan opacity="0">向
      <animate attributeName="opacity" values="0; 1" dur="0.1s" begin="12.3s" fill="freeze"/>
    </tspan><tspan opacity="0">母
      <animate attributeName="opacity" values="0; 1" dur="0.1s" begin="12.5s" fill="freeze"/>
    </tspan><tspan opacity="0">亲
      <animate attributeName="opacity" values="0; 1" dur="0.1s" begin="12.7s" fill="freeze"/>
    </tspan><tspan opacity="0">借
      <animate attributeName="opacity" values="0; 1" dur="0.1s" begin="13.0s" fill="freeze"/>
    </tspan><tspan opacity="0">用
      <animate attributeName="opacity" values="0; 1" dur="0.1s" begin="13.3s" fill="freeze"/>
    </tspan>
  </text>
  
  <!-- 争议延伸文字 -->
  <text id="controversy-extension" x="960" y="850" font-family="Arial, sans-serif" font-size="40" text-anchor="middle" dominant-baseline="middle" opacity="0">
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="0.5s"
      begin="15s"
      fill="freeze"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="0,30; 0,0"
      dur="0.5s"
      begin="15s"
      calcMode="spline"
      keySplines="0.25 0.1 0.25 1"
      fill="freeze"/>
    <tspan fill="#0066CC">争议延伸：奢侈品</tspan>
    <tspan fill="#666666"> → </tspan>
    <tspan fill="#FF0000">家庭财富来源</tspan>
  </text>
  
  <!-- 向上箭头动画 -->
  <g id="arrow-animation" opacity="0">
    <path d="M 960 820 L 960 780 M 950 790 L 960 780 L 970 790" 
          stroke="#FF6600" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="0.5s"
      begin="16s"
      fill="freeze"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="0,20; 0,0"
      dur="0.5s"
      begin="16s"
      calcMode="spline"
      keySplines="0.25 0.1 0.25 1"
      fill="freeze"/>
  </g>
  
  <!-- 补充说明文字 -->
  <text id="supplementary-text" x="960" y="900" font-family="Arial, sans-serif" font-size="24" font-style="italic" fill="#888888" text-anchor="middle" dominant-baseline="middle" opacity="0">
    引发对家庭背景的质疑
    <animate
      attributeName="opacity"
      values="0; 1"
      dur="0.5s"
      begin="16.5s"
      fill="freeze"/>
    <animateTransform
      attributeName="transform"
      type="translate"
      values="0,20; 0,0"
      dur="0.5s"
      begin="16.5s"
      calcMode="spline"
      keySplines="0.42 0 0.58 1"
      fill="freeze"/>
  </text>
  
</svg> 